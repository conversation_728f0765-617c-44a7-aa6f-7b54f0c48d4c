; 
[main]

; Require senders to conform to RFC 1869 and RFC 821 when sending the MAIL FROM and RCPT TO commands. In particular, the inclusion of spurious spaces or missing angle brackets will be rejected.
; strict_rfc1869 = false

; Advertise support for SMTPUTF8 (RFC-6531)
; smtputf8=true


[haproxy]
; Array: hosts or CIDRs that <PERSON><PERSON> should enable the PROXY protocol from. See docs/HAProxy for format
hosts[] =
; hosts[] = *********
; hosts[] = *********
; hosts[] = [2001:db8::1]
; hosts[] = [2001:db8::2]


[headers]
; add_received=true
; clean_auth_results=true

; show_version=true

max_lines=1000

max_received=100


[max]
; Integer. The maximum SIZE of an email
bytes=26214400

; Integer. Limit a potential denial of service in potentially hostile emails.
mime_parts=1000

; Integer. The maximum length of lines in SMTP session commands (e.g. RCPT, HELO etc). Defaults to 512 (bytes) as mandated by RFC 5321 §*******.4. Clients exceeding this limit will be immediately disconnected with a "521 Command line too long" error.
line_length=512

; Integer. The maximum length of lines in the DATA section of emails. Defaults to 992 (bytes), the limit set by Sendmail. When this limit is exceeded the three bytes "\r\n " (0x0d 0x0a 0x20) are inserted into the stream to "fix" it. This has the potential to "break" some email, but makes it more likely to be accepted by upstream/downstream services, and is the same behaviour as Sendmail. Also when the data line length limit is exceeded `transaction.notes.data_line_length_exceeded` is set to `true`.
data_line_length=992


[message]
; Array. The greeting used when a client connects.
; greeting[]=My Custom
; greeting[]=Greeting Message

helo=IronRelay

; String. Override the default connection close message.
close=closing connection. Have a good day.


[uuid]
; integer, how many UUID chars to show.
; 0 = none, 6 is enough to be unique per day, 40 will include the
;     full connection and transaction UUID
banner_chars=6

; include N characters of the uuid (in brackets) at the start of each line of the deny message
deny_chars=0
