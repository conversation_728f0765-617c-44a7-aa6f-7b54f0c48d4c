// Custom SMTP Banner Plugin for Haraka
// Customizes the SMTP greeting banner to show custom hostname and software

exports.register = function () {
    const plugin = this;

    // Load configuration
    plugin.load_config();

    plugin.loginfo('Custom Banner plugin loaded');
};

exports.load_config = function () {
    const plugin = this;

    plugin.cfg = plugin.config.get('custom_banner.ini', {
        booleans: [
            '+main.enabled'
        ]
    }, function() {
        plugin.load_config();
    });

    // Set defaults
    if (!plugin.cfg.main) plugin.cfg.main = {};
    if (plugin.cfg.main.enabled === undefined) plugin.cfg.main.enabled = true;
    if (!plugin.cfg.main.hostname) plugin.cfg.main.hostname = 'mail.mosh.wtf';
    if (!plugin.cfg.main.software) plugin.cfg.main.software = 'IronRelay SMTP';

    plugin.loginfo('Custom Banner configuration loaded');
    plugin.loginfo(`Will use hostname: ${plugin.cfg.main.hostname}, software: ${plugin.cfg.main.software}`);
};

// Hook into the connection to customize the banner
exports.hook_connect_init = function (next, connection) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    const hostname = plugin.cfg.main.hostname;
    const software = plugin.cfg.main.software;

    // Generate a transaction ID similar to the original
    const transaction_id = Math.random().toString(36).substr(2, 6).toUpperCase();

    // Custom greeting message
    const custom_banner = `220 ${hostname} ESMTP ${software} ready (${transaction_id})`;

    // Send the custom banner immediately
    connection.respond(custom_banner);

    plugin.loginfo(`Custom banner sent: ${custom_banner}`);

    // Don't call next() to prevent the default banner
    return;
};
