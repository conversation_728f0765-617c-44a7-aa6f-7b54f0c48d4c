// Custom SMTP Banner Plugin for Haraka
// Simple banner override using environment variables

exports.register = function () {
    const plugin = this;

    // Load configuration
    plugin.load_config();

    plugin.loginfo('Custom Banner plugin loaded - Simple version');
};

exports.load_config = function () {
    const plugin = this;

    plugin.cfg = plugin.config.get('custom_banner.ini', {
        booleans: [
            '+main.enabled'
        ]
    }, function() {
        plugin.load_config();
    });

    // Set defaults
    if (!plugin.cfg.main) plugin.cfg.main = {};
    if (plugin.cfg.main.enabled === undefined) plugin.cfg.main.enabled = true;
    if (!plugin.cfg.main.hostname) plugin.cfg.main.hostname = 'mail.mosh.wtf';
    if (!plugin.cfg.main.software) plugin.cfg.main.software = 'IronRelay SMTP';

    plugin.loginfo('Custom Banner configuration loaded');
    plugin.loginfo(`Will use hostname: ${plugin.cfg.main.hostname}, software: ${plugin.cfg.main.software}`);
};

// Override banner using connection hook - safer approach
exports.hook_connect = function (next, connection) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    // Set custom banner properties on the connection
    const hostname = plugin.cfg.main.hostname;
    const software = plugin.cfg.main.software;

    // Try to override banner-related properties
    if (connection.server) {
        connection.server.banner_hostname = hostname;
        connection.server.banner_software = software;
    }

    plugin.loginfo(`Custom banner settings applied: ${hostname} / ${software}`);

    next();
};
