// Custom SMTP Banner Plugin for Haraka
// Customizes the SMTP greeting banner

exports.register = function () {
    const plugin = this;
    
    // Load configuration
    plugin.load_config();
    
    plugin.loginfo('Custom Banner plugin loaded');
};

exports.load_config = function () {
    const plugin = this;
    
    plugin.cfg = plugin.config.get('custom_banner.ini', {
        booleans: [
            '+main.enabled'
        ]
    }, function() {
        plugin.load_config();
    });
    
    // Set defaults
    if (!plugin.cfg.main) plugin.cfg.main = {};
    if (plugin.cfg.main.enabled === undefined) plugin.cfg.main.enabled = true;
    if (!plugin.cfg.main.hostname) plugin.cfg.main.hostname = 'mail.mosh.wtf';
    if (!plugin.cfg.main.software) plugin.cfg.main.software = 'IronRelay SMTP';
    if (!plugin.cfg.main.greeting_code) plugin.cfg.main.greeting_code = '220';
    
    plugin.loginfo('Custom Banner configuration loaded');
};

// Hook into the connection to customize the banner
exports.hook_connect = function (next, connection) {
    const plugin = this;
    
    if (!plugin.cfg.main.enabled) return next();
    
    // Override the connection's banner
    const hostname = plugin.cfg.main.hostname;
    const software = plugin.cfg.main.software;
    const code = plugin.cfg.main.greeting_code;
    
    // Custom greeting message
    const custom_banner = `${code} ${hostname} ESMTP ${software} ready`;
    
    // Set the custom banner
    connection.greeting = custom_banner;
    
    plugin.logdebug(`Custom banner set: ${custom_banner}`);
    
    next();
};

// Alternative hook if the above doesn't work
exports.hook_greeting = function (next, connection) {
    const plugin = this;
    
    if (!plugin.cfg.main.enabled) return next();
    
    const hostname = plugin.cfg.main.hostname;
    const software = plugin.cfg.main.software;
    const code = plugin.cfg.main.greeting_code;
    
    // Custom greeting message
    const custom_banner = `${code} ${hostname} ESMTP ${software} ready`;
    
    // Override the greeting
    connection.respond(custom_banner);
    
    plugin.logdebug(`Custom greeting sent: ${custom_banner}`);
    
    // Don't call next() to prevent default greeting
    return;
};
