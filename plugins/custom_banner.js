// Custom SMTP Banner Plugin for Haraka
// Overrides the SMTP greeting banner

exports.register = function () {
    const plugin = this;

    // Load configuration
    plugin.load_config();

    // Register hooks
    plugin.register_hook('init_master', 'setup_banner_override');

    plugin.loginfo('Custom Banner plugin loaded');
};

exports.load_config = function () {
    const plugin = this;

    plugin.cfg = plugin.config.get('custom_banner.ini', {
        booleans: [
            '+main.enabled'
        ]
    }, function() {
        plugin.load_config();
    });

    // Set defaults
    if (!plugin.cfg.main) plugin.cfg.main = {};
    if (plugin.cfg.main.enabled === undefined) plugin.cfg.main.enabled = true;
    if (!plugin.cfg.main.hostname) plugin.cfg.main.hostname = 'mail.mosh.wtf';
    if (!plugin.cfg.main.software) plugin.cfg.main.software = 'IronRelay SMTP';

    plugin.loginfo('Custom Banner configuration loaded');
    plugin.loginfo(`Will use hostname: ${plugin.cfg.main.hostname}, software: ${plugin.cfg.main.software}`);
};

// Setup banner override at server initialization
exports.setup_banner_override = function (next, server) {
    const plugin = this;

    if (!plugin.cfg.main.enabled) return next();

    // Override the server configuration to use custom banner
    if (server.config) {
        // Patch the config.get method to return our custom banner settings
        const original_config_get = server.config.get;
        server.config.get = function(file, type, cb) {
            const result = original_config_get.call(this, file, type, cb);

            // Override smtp.ini settings
            if (file === 'smtp.ini') {
                if (!result) result = {};
                if (!result.main) result.main = {};

                // Set custom banner settings
                result.main.banner_hostname = plugin.cfg.main.hostname;
                result.main.banner_includes_version = false;
                result.main.banner_software = plugin.cfg.main.software;

                plugin.logdebug('Injected custom banner settings into smtp.ini');
            }

            return result;
        };
    }

    plugin.loginfo('Banner configuration override installed');
    next();
};
