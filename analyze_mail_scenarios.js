#!/usr/bin/env node

// Analyze mail scenarios from the email_transactions collection
const { MongoClient } = require('mongodb');

const config = {
    host: 'localhost',
    port: 27017,
    database: 'haraka_email_logs'
};

const connection_string = `mongodb://${config.host}:${config.port}/${config.database}`;

async function analyzeMailScenarios() {
    let client;
    
    try {
        console.log('🔍 Analyzing mail scenarios from email_transactions collection...\n');
        
        client = await MongoClient.connect(connection_string, {
            connectTimeoutMS: 5000,
            socketTimeoutMS: 30000
        });
        
        const db = client.db(config.database);
        const transactions = db.collection('email_transactions');
        
        // Get all transactions sorted by timestamp
        console.log('📋 All transactions with full details:\n');
        console.log('='.repeat(100));
        
        const allTransactions = await transactions.find()
            .sort({ timestamp: -1 })
            .toArray();
        
        console.log(`Found ${allTransactions.length} total transactions\n`);
        
        // Analyze each transaction type
        const statusGroups = {};
        
        allTransactions.forEach((tx, index) => {
            const status = tx.status || 'unknown';
            if (!statusGroups[status]) {
                statusGroups[status] = [];
            }
            statusGroups[status].push(tx);
        });
        
        // Display detailed analysis for each status type
        for (const [status, transactions] of Object.entries(statusGroups)) {
            console.log(`\n🏷️  STATUS: ${status.toUpperCase()} (${transactions.length} transactions)`);
            console.log('─'.repeat(80));
            
            transactions.forEach((tx, index) => {
                console.log(`\n📄 Transaction ${index + 1}:`);
                console.log(`   ID: ${tx.transaction_id || tx._id}`);
                console.log(`   Timestamp: ${tx.timestamp ? new Date(tx.timestamp).toISOString() : 'N/A'}`);
                
                // Sender information
                if (tx.mail_from) {
                    console.log(`   From: ${tx.mail_from.address || 'N/A'} (${tx.mail_from.original || 'N/A'})`);
                } else if (tx.sender) {
                    console.log(`   Sender: ${tx.sender.original || 'N/A'} (user: ${tx.sender.user || 'N/A'}, host: ${tx.sender.host || 'N/A'})`);
                } else {
                    console.log(`   From: N/A`);
                }
                
                // Recipient information
                if (tx.rcpt_to && tx.rcpt_to.length > 0) {
                    console.log(`   To: ${tx.rcpt_to.map(r => r.address || r.original).join(', ')}`);
                } else if (tx.recipient && tx.recipient.length > 0) {
                    console.log(`   Recipients:`);
                    tx.recipient.forEach((r, i) => {
                        console.log(`     ${i + 1}. ${r.original || 'N/A'} (host: ${r.host || 'N/A'})`);
                        if (r.dsn_code) console.log(`        DSN Code: ${r.dsn_code}`);
                        if (r.dsn_msg) console.log(`        DSN Message: ${r.dsn_msg}`);
                        if (r.dsn_status) console.log(`        DSN Status: ${r.dsn_status}`);
                        if (r.dsn_action) console.log(`        DSN Action: ${r.dsn_action}`);
                    });
                } else {
                    console.log(`   To: N/A`);
                }
                
                // Connection information
                if (tx.connection) {
                    console.log(`   Connection: ${tx.connection.remote_ip || 'N/A'} (${tx.connection.remote_host || 'N/A'})`);
                } else {
                    console.log(`   Connection: N/A`);
                }
                
                // Message information
                if (tx.message) {
                    console.log(`   Subject: ${tx.message.subject || 'N/A'}`);
                    console.log(`   Size: ${tx.message.size || 0} bytes`);
                    console.log(`   Message-ID: ${tx.message.message_id || 'N/A'}`);
                }
                
                // Status-specific information
                if (tx.defer_reason) {
                    console.log(`   Defer Reason: ${tx.defer_reason}`);
                }
                if (tx.rejection_reason) {
                    console.log(`   Rejection Reason: ${tx.rejection_reason}`);
                }
                if (tx.bounce_reason) {
                    console.log(`   Bounce Reason: ${tx.bounce_reason}`);
                }
                
                // Authentication information
                if (tx.authentication) {
                    console.log(`   Authentication:`);
                    if (tx.authentication.spf) console.log(`     SPF: ${JSON.stringify(tx.authentication.spf)}`);
                    if (tx.authentication.dkim) console.log(`     DKIM: ${JSON.stringify(tx.authentication.dkim)}`);
                    if (tx.authentication.dmarc) console.log(`     DMARC: ${JSON.stringify(tx.authentication.dmarc)}`);
                }
                
                // Processing information
                console.log(`   Processing Time: ${tx.processing_time || 0}ms`);
                
                // Headers (if present)
                if (tx.headers && Object.keys(tx.headers).length > 0) {
                    console.log(`   Headers: ${Object.keys(tx.headers).length} headers present`);
                    console.log(`     Sample headers: ${Object.keys(tx.headers).slice(0, 3).join(', ')}`);
                }
                
                // Timestamps (if present)
                if (tx.timestamps) {
                    console.log(`   Timestamps:`);
                    Object.entries(tx.timestamps).forEach(([event, time]) => {
                        if (time) console.log(`     ${event}: ${new Date(time).toISOString()}`);
                    });
                }
                
                // Errors (if present)
                if (tx.errors && tx.errors.length > 0) {
                    console.log(`   Errors: ${tx.errors.length} errors`);
                    tx.errors.forEach((error, i) => {
                        console.log(`     ${i + 1}. ${error}`);
                    });
                }
                
                // Raw document structure for debugging
                console.log(`   Raw Keys: ${Object.keys(tx).join(', ')}`);
            });
        }
        
        // Summary analysis
        console.log(`\n\n📊 SUMMARY ANALYSIS`);
        console.log('='.repeat(50));
        console.log(`Total transactions: ${allTransactions.length}`);
        console.log(`Status distribution:`);
        Object.entries(statusGroups).forEach(([status, txs]) => {
            console.log(`  ${status}: ${txs.length} transactions`);
        });
        
        // Data structure analysis
        console.log(`\nData structure patterns:`);
        const hasMailFrom = allTransactions.filter(tx => tx.mail_from).length;
        const hasSender = allTransactions.filter(tx => tx.sender).length;
        const hasRcptTo = allTransactions.filter(tx => tx.rcpt_to).length;
        const hasRecipient = allTransactions.filter(tx => tx.recipient).length;
        const hasConnection = allTransactions.filter(tx => tx.connection).length;
        const hasMessage = allTransactions.filter(tx => tx.message).length;
        
        console.log(`  mail_from field: ${hasMailFrom} transactions`);
        console.log(`  sender field: ${hasSender} transactions`);
        console.log(`  rcpt_to field: ${hasRcptTo} transactions`);
        console.log(`  recipient field: ${hasRecipient} transactions`);
        console.log(`  connection field: ${hasConnection} transactions`);
        console.log(`  message field: ${hasMessage} transactions`);
        
    } catch (error) {
        console.error('❌ Error analyzing mail scenarios:', error.message);
    } finally {
        if (client) {
            await client.close();
            console.log('\n📝 Database connection closed');
        }
    }
}

// Run the analysis
analyzeMailScenarios();
