import { format, formatDistanceToNow, isValid } from 'date-fns';

// Format date for display
export const formatDate = (date, formatString = 'MMM dd, yyyy HH:mm:ss') => {
  if (!date) return 'N/A';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValid(dateObj)) return 'Invalid Date';
  
  return format(dateObj, formatString);
};

// Format relative time (e.g., "2 hours ago")
export const formatRelativeTime = (date) => {
  if (!date) return 'N/A';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValid(dateObj)) return 'Invalid Date';
  
  return formatDistanceToNow(dateObj, { addSuffix: true });
};

// Format email address for display
export const formatEmail = (email) => {
  if (!email) return 'N/A';
  
  // Remove angle brackets if present
  return email.replace(/[<>]/g, '').trim();
};

// Format subject line
export const formatSubject = (subject) => {
  if (!subject) return 'No Subject';
  
  // Decode basic MIME encoded subjects
  let decoded = subject;
  
  // Handle =?utf-8?q?...?= encoding
  const mimeRegex = /=\?([^?]+)\?([qb])\?([^?]*)\?=/gi;
  decoded = decoded.replace(mimeRegex, (match, charset, encoding, text) => {
    if (encoding.toLowerCase() === 'q') {
      // Quoted-printable decoding (basic)
      return text.replace(/=([0-9A-F]{2})/gi, (match, hex) => {
        return String.fromCharCode(parseInt(hex, 16));
      }).replace(/_/g, ' ');
    }
    return text;
  });
  
  return decoded.trim();
};

// Format file size
export const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Format processing time
export const formatProcessingTime = (ms) => {
  if (!ms && ms !== 0) return 'N/A';
  
  if (ms < 1000) {
    return `${ms}ms`;
  } else {
    return `${(ms / 1000).toFixed(2)}s`;
  }
};

// Format status with appropriate styling
export const getStatusBadge = (status) => {
  const statusMap = {
    'delivered': { label: 'Delivered', className: 'badge-success' },
    'relayed': { label: 'Relayed', className: 'badge-info' },
    'queued': { label: 'Queued', className: 'badge-warning' },
    'bounced': { label: 'Bounced', className: 'badge-danger' },
    'rejected': { label: 'Rejected', className: 'badge-danger' },
    'deferred': { label: 'Deferred', className: 'badge-warning' },
    'failed': { label: 'Failed', className: 'badge-danger' },
    'accepted': { label: 'Accepted', className: 'badge-success' },
    'disconnected': { label: 'Disconnected', className: 'badge-warning' },
    'in_progress': { label: 'In Progress', className: 'badge-info' },
  };

  return statusMap[status] || { label: status || 'Unknown', className: 'badge-info' };
};

// Format IP address
export const formatIP = (ip) => {
  if (!ip) return 'N/A';
  return ip;
};

// Format hostname
export const formatHostname = (hostname) => {
  if (!hostname) return 'N/A';
  return hostname;
};

// Truncate text with ellipsis
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Format header value for display
export const formatHeaderValue = (value) => {
  if (!value) return 'N/A';
  
  if (Array.isArray(value)) {
    return value.join(', ').trim();
  }
  
  return String(value).trim();
};

// Extract domain from email
export const extractDomain = (email) => {
  if (!email) return 'N/A';
  
  const cleaned = formatEmail(email);
  const atIndex = cleaned.lastIndexOf('@');
  
  if (atIndex === -1) return 'N/A';
  
  return cleaned.substring(atIndex + 1);
};

// Format recipient list
export const formatRecipients = (recipients) => {
  if (!recipients || !Array.isArray(recipients)) return 'N/A';
  
  if (recipients.length === 0) return 'No recipients';
  
  if (recipients.length === 1) {
    return formatEmail(recipients[0].address || recipients[0]);
  }
  
  const first = formatEmail(recipients[0].address || recipients[0]);
  return `${first} (+${recipients.length - 1} more)`;
};
