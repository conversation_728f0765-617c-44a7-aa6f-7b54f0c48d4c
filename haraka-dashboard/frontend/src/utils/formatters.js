import { format, formatDistanceToNow, isValid } from 'date-fns';

// Format date for display
export const formatDate = (date, formatString = 'MMM dd, yyyy HH:mm:ss') => {
  if (!date) return 'N/A';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValid(dateObj)) return 'Invalid Date';
  
  return format(dateObj, formatString);
};

// Format relative time (e.g., "2 hours ago")
export const formatRelativeTime = (date) => {
  if (!date) return 'N/A';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValid(dateObj)) return 'Invalid Date';
  
  return formatDistanceToNow(dateObj, { addSuffix: true });
};

// Format email address for display
export const formatEmail = (email) => {
  if (!email) return 'N/A';
  
  // Remove angle brackets if present
  return email.replace(/[<>]/g, '').trim();
};

// Format subject line
export const formatSubject = (subject) => {
  if (!subject) return 'No Subject';
  
  // Decode basic MIME encoded subjects
  let decoded = subject;
  
  // Handle =?utf-8?q?...?= encoding
  const mimeRegex = /=\?([^?]+)\?([qb])\?([^?]*)\?=/gi;
  decoded = decoded.replace(mimeRegex, (match, charset, encoding, text) => {
    if (encoding.toLowerCase() === 'q') {
      // Quoted-printable decoding (basic)
      return text.replace(/=([0-9A-F]{2})/gi, (match, hex) => {
        return String.fromCharCode(parseInt(hex, 16));
      }).replace(/_/g, ' ');
    }
    return text;
  });
  
  return decoded.trim();
};

// Format file size
export const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Format processing time
export const formatProcessingTime = (ms) => {
  if (!ms && ms !== 0) return 'N/A';
  
  if (ms < 1000) {
    return `${ms}ms`;
  } else {
    return `${(ms / 1000).toFixed(2)}s`;
  }
};

// Consolidate intermediate statuses to final display statuses
export const consolidateTransactionStatus = (transaction) => {
  if (!transaction) return 'unknown';

  const status = transaction.status;
  const hasRejectionReason = transaction.rejection_reason;
  const hasErrors = transaction.errors && transaction.errors.length > 0;
  const authFailures = transaction.authentication && (
    transaction.authentication.spf?.result === 'fail' ||
    transaction.authentication.dkim?.result === 'fail' ||
    transaction.authentication.dmarc?.result === 'fail'
  );

  // Map statuses to final display states
  switch (status) {
    case 'delivered':
    case 'relayed':
    case 'accepted':
    case 'queued': // Queued emails that completed successfully
      return 'delivered';

    case 'rejected':
    case 'bounced':
      // Check if rejection was due to authentication failures
      if (authFailures || (hasRejectionReason &&
          (transaction.rejection_reason.toLowerCase().includes('spf') ||
           transaction.rejection_reason.toLowerCase().includes('dkim') ||
           transaction.rejection_reason.toLowerCase().includes('dmarc') ||
           transaction.rejection_reason.toLowerCase().includes('authentication')))) {
        return 'blocked';
      }
      return 'blocked'; // All rejections are considered blocked

    case 'deferred':
      return 'deferred'; // Keep deferred as its own status

    case 'failed':
      return 'failed';

    case 'disconnected':
      return 'disconnected';

    case 'in_progress':
      // If still in progress but has errors, consider it failed
      if (hasErrors) return 'failed';
      return 'disconnected'; // Assume disconnected if still in progress

    default:
      return 'unknown';
  }
};

// Format status with appropriate styling using consolidated statuses
export const getStatusBadge = (status, transaction = null) => {
  // If transaction object is provided, use consolidated status
  const finalStatus = transaction ? consolidateTransactionStatus(transaction) : status;

  const statusMap = {
    'delivered': { label: 'Delivered', className: 'badge-success' },
    'blocked': { label: 'Blocked', className: 'badge-danger' },
    'deferred': { label: 'Deferred', className: 'badge-warning' },
    'failed': { label: 'Failed', className: 'badge-danger' },
    'disconnected': { label: 'Disconnected', className: 'badge-warning' },
    'unknown': { label: 'Unknown', className: 'badge-info' },

    // Legacy statuses for backward compatibility
    'relayed': { label: 'Delivered', className: 'badge-success' },
    'queued': { label: 'Delivered', className: 'badge-success' },
    'rejected': { label: 'Blocked', className: 'badge-danger' },
    'bounced': { label: 'Blocked', className: 'badge-danger' },
    'accepted': { label: 'Delivered', className: 'badge-success' },
    'in_progress': { label: 'Disconnected', className: 'badge-warning' },
  };

  return statusMap[finalStatus] || { label: finalStatus || 'Unknown', className: 'badge-info' };
};

// Format IP address
export const formatIP = (ip) => {
  if (!ip) return 'N/A';
  return ip;
};

// Format hostname
export const formatHostname = (hostname) => {
  if (!hostname) return 'N/A';
  return hostname;
};

// Truncate text with ellipsis
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Format header value for display
export const formatHeaderValue = (value) => {
  if (!value) return 'N/A';

  if (Array.isArray(value)) {
    return value.join(', ').trim();
  }

  return String(value).trim();
};

// Format defer reason (handle "[object Object]" string)
export const formatDeferReason = (deferReason, transaction = null) => {
  if (!deferReason) {
    // If no defer reason but we have recipient DSN info, use that
    if (transaction?.recipient?.[0]?.dsn_msg) {
      return transaction.recipient[0].dsn_msg;
    }
    return 'No defer reason specified';
  }

  // Handle the "[object Object]" string case
  if (deferReason === '[object Object]') {
    // Try to get meaningful info from other fields
    if (transaction?.recipient?.[0]?.dsn_msg) {
      return transaction.recipient[0].dsn_msg;
    }
    if (transaction?.recipient?.[0]?.dsn_code) {
      return `Delivery failed with code ${transaction.recipient[0].dsn_code}`;
    }
    return 'Defer reason was not properly logged';
  }

  return String(deferReason).trim();
};

// Extract domain from email
export const extractDomain = (email) => {
  if (!email) return 'N/A';
  
  const cleaned = formatEmail(email);
  const atIndex = cleaned.lastIndexOf('@');
  
  if (atIndex === -1) return 'N/A';
  
  return cleaned.substring(atIndex + 1);
};

// Format recipient list
export const formatRecipients = (recipients) => {
  if (!recipients || !Array.isArray(recipients)) return 'N/A';
  
  if (recipients.length === 0) return 'No recipients';
  
  if (recipients.length === 1) {
    return formatEmail(recipients[0].address || recipients[0]);
  }
  
  const first = formatEmail(recipients[0].address || recipients[0]);
  return `${first} (+${recipients.length - 1} more)`;
};
