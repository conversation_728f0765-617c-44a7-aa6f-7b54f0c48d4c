import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Search, 
  Filter, 
  ChevronLeft, 
  ChevronRight, 
  Mail, 
  Calendar,
  Download,
  RefreshCw,
  Eye,
  ArrowUpDown,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { useTransactions } from '../hooks/useApi';
import {
  formatDate,
  formatRelativeTime,
  getStatusBadge,
  formatEmail,
  formatSubject,
  formatFileSize,
  formatProcessingTime,
  truncateText
} from '../utils/formatters';

const Transactions = () => {
  const [filters, setFilters] = useState({
    page: 1,
    limit: 25,
    sortBy: 'timestamp',
    sortOrder: 'desc',
    search: '',
    status: '',
    dateFrom: '',
    dateTo: ''
  });

  const [searchInput, setSearchInput] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const { data, loading, error, refetch } = useTransactions();

  // Fetch data when filters change
  useEffect(() => {
    refetch(filters);
  }, [filters]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    setFilters(prev => ({ ...prev, search: searchInput, page: 1 }));
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle sorting
  const handleSort = (column) => {
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortBy === column && prev.sortOrder === 'desc' ? 'asc' : 'desc',
      page: 1
    }));
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      page: 1,
      limit: 25,
      sortBy: 'timestamp',
      sortOrder: 'desc',
      search: '',
      status: '',
      dateFrom: '',
      dateTo: ''
    });
    setSearchInput('');
  };

  // Get sort icon
  const getSortIcon = (column) => {
    if (filters.sortBy !== column) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return filters.sortOrder === 'desc' ? 
      <ArrowDown className="h-4 w-4" /> : 
      <ArrowUp className="h-4 w-4" />;
  };

  const SortableHeader = ({ column, children, className = '' }) => (
    <th
      className={`table-header-cell ${className}`}
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center space-x-1">
        <span>{children}</span>
        {getSortIcon(column)}
      </div>
    </th>
  );

  if (error) {
    return (
      <div className="text-center py-12">
        <Mail className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading transactions</h3>
        <p className="mt-1 text-sm text-gray-500">{error}</p>
        <button
          onClick={() => refetch(filters)}
          className="mt-4 btn-primary"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Email Transactions
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            {data?.pagination ? 
              `Showing ${((data.pagination.currentPage - 1) * data.pagination.limit) + 1}-${Math.min(data.pagination.currentPage * data.pagination.limit, data.pagination.totalCount)} of ${data.pagination.totalCount} transactions` :
              'Loading transactions...'
            }
          </p>
        </div>
        <div className="mt-4 flex space-x-3 md:mt-0 md:ml-4">
          <button
            onClick={() => refetch(filters)}
            className="btn-outline"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button className="btn-outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card p-6">
        <div className="space-y-4">
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by sender, recipient, subject..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="input pl-10"
                />
              </div>
            </div>
            <button type="submit" className="btn-primary">
              Search
            </button>
            <button
              type="button"
              onClick={() => setShowFilters(!showFilters)}
              className="btn-outline"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>
          </form>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 gap-4 pt-4 border-t border-gray-200 sm:grid-cols-2 lg:grid-cols-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="select"
                >
                  <option value="">All Statuses</option>
                  <option value="delivered">Delivered</option>
                  <option value="relayed">Relayed</option>
                  <option value="queued">Queued</option>
                  <option value="bounced">Bounced</option>
                  <option value="rejected">Rejected</option>
                  <option value="deferred">Deferred</option>
                  <option value="failed">Failed</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  From Date
                </label>
                <input
                  type="datetime-local"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="input"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  To Date
                </label>
                <input
                  type="datetime-local"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="input"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Per Page
                </label>
                <select
                  value={filters.limit}
                  onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                  className="select"
                >
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
              </div>

              <div className="sm:col-span-2 lg:col-span-4">
                <button
                  onClick={clearFilters}
                  className="btn-outline"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Transactions Table */}
      <div className="card overflow-hidden">
        {loading ? (
          <div className="p-12 text-center">
            <div className="loading-spinner mx-auto"></div>
            <p className="mt-4 text-sm text-gray-500">Loading transactions...</p>
          </div>
        ) : data?.transactions?.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <SortableHeader column="timestamp">Date/Time</SortableHeader>
                    <SortableHeader column="mail_from.address">From</SortableHeader>
                    <th className="table-header-cell">To</th>
                    <SortableHeader column="message.subject">Subject</SortableHeader>
                    <SortableHeader column="status">Status</SortableHeader>
                    <SortableHeader column="message.size">Size</SortableHeader>
                    <SortableHeader column="processing_time">Time</SortableHeader>
                    <th className="table-header-cell">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {data.transactions.map((transaction) => (
                    <tr key={transaction._id} className="table-row">
                      <td className="table-cell">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {formatDate(transaction.timestamp, 'MMM dd, HH:mm')}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatRelativeTime(transaction.timestamp)}
                          </div>
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">
                          {transaction.mail_from?.address ?
                            truncateText(formatEmail(transaction.mail_from.address), 30) :
                            <span className="text-gray-400 italic">
                              {transaction.status === 'disconnected' ? 'Connection lost' : 'N/A'}
                            </span>
                          }
                        </div>
                        <div className="text-sm text-gray-500">
                          {transaction.connection?.remote_ip || 'Unknown IP'}
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">
                          {transaction.rcpt_to?.length > 0 ? (
                            <>
                              {truncateText(formatEmail(transaction.rcpt_to[0]?.address), 30)}
                              {transaction.rcpt_to.length > 1 && (
                                <span className="text-gray-500"> (+{transaction.rcpt_to.length - 1})</span>
                              )}
                            </>
                          ) : (
                            <span className="text-gray-400 italic">
                              {transaction.status === 'disconnected' ? 'Connection lost' : 'No recipients'}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">
                          {transaction.message?.subject ?
                            truncateText(formatSubject(transaction.message.subject), 40) :
                            <span className="text-gray-400 italic">
                              {transaction.status === 'disconnected' ? 'Connection lost' : 'No Subject'}
                            </span>
                          }
                        </div>
                      </td>
                      <td className="table-cell">
                        <span className={`badge ${getStatusBadge(transaction.status, transaction).className}`}>
                          {getStatusBadge(transaction.status, transaction).label}
                        </span>
                      </td>
                      <td className="table-cell text-sm text-gray-500">
                        {formatFileSize(transaction.message?.size)}
                      </td>
                      <td className="table-cell text-sm text-gray-500">
                        {formatProcessingTime(transaction.processing_time)}
                      </td>
                      <td className="table-cell">
                        <Link
                          to={`/transactions/${transaction._id}`}
                          className="inline-flex items-center justify-center p-2 rounded-md text-primary-600 hover:text-primary-900 hover:bg-primary-50 transition-colors duration-200"
                          title="View transaction details"
                          aria-label={`View details for transaction ${transaction._id}`}
                        >
                          <Eye className="h-4 w-4" />
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {data.pagination && data.pagination.totalPages > 1 && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1 flex justify-between sm:hidden">
                    <button
                      onClick={() => handlePageChange(data.pagination.currentPage - 1)}
                      disabled={!data.pagination.hasPrevPage}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => handlePageChange(data.pagination.currentPage + 1)}
                      disabled={!data.pagination.hasNextPage}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing page <span className="font-medium">{data.pagination.currentPage}</span> of{' '}
                        <span className="font-medium">{data.pagination.totalPages}</span>
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <button
                          onClick={() => handlePageChange(data.pagination.currentPage - 1)}
                          disabled={!data.pagination.hasPrevPage}
                          className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ChevronLeft className="h-5 w-5" />
                        </button>

                        {/* Page numbers */}
                        {Array.from({ length: Math.min(5, data.pagination.totalPages) }, (_, i) => {
                          const pageNum = Math.max(1, data.pagination.currentPage - 2) + i;
                          if (pageNum > data.pagination.totalPages) return null;

                          return (
                            <button
                              key={pageNum}
                              onClick={() => handlePageChange(pageNum)}
                              className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                pageNum === data.pagination.currentPage
                                  ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                              }`}
                            >
                              {pageNum}
                            </button>
                          );
                        })}

                        <button
                          onClick={() => handlePageChange(data.pagination.currentPage + 1)}
                          disabled={!data.pagination.hasNextPage}
                          className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ChevronRight className="h-5 w-5" />
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="p-12 text-center">
            <Mail className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filters.search || filters.status || filters.dateFrom || filters.dateTo
                ? 'Try adjusting your search criteria or filters.'
                : 'No email transactions have been recorded yet.'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Transactions;
