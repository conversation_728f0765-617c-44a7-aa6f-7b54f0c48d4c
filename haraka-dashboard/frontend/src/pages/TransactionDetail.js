import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { 
  ArrowLeft, 
  Mail, 
  Clock, 
  Server, 
  Shield, 
  FileText,
  User,
  Globe,
  Calendar,
  Hash,
  AlertCircle
} from 'lucide-react';
import { useTransaction } from '../hooks/useApi';
import {
  formatDate,
  formatRelativeTime,
  getStatusBadge,
  consolidateTransactionStatus,
  formatEmail,
  formatSubject,
  formatFileSize,
  formatProcessingTime,
  formatHeaderValue
} from '../utils/formatters';

const TransactionDetail = () => {
  const { id } = useParams();
  const { data: transaction, loading, error } = useTransaction(id);

  // Get consolidated status for conditional rendering
  const consolidatedStatus = transaction ? consolidateTransactionStatus(transaction) : null;

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="loading-spinner mx-auto"></div>
        <p className="mt-4 text-sm text-gray-500">Loading transaction details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading transaction</h3>
        <p className="mt-1 text-sm text-gray-500">{error}</p>
        <Link to="/transactions" className="mt-4 btn-primary">
          Back to Transactions
        </Link>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className="text-center py-12">
        <Mail className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Transaction not found</h3>
        <p className="mt-1 text-sm text-gray-500">The requested transaction could not be found.</p>
        <Link to="/transactions" className="mt-4 btn-primary">
          Back to Transactions
        </Link>
      </div>
    );
  }

  const InfoCard = ({ title, icon: Icon, children }) => (
    <div className="card p-6">
      <div className="flex items-center mb-4">
        <Icon className="h-5 w-5 text-gray-400 mr-2" />
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
      </div>
      {children}
    </div>
  );

  const InfoRow = ({ label, value, className = '' }) => (
    <div className={`flex justify-between py-2 ${className}`}>
      <dt className="text-sm font-medium text-gray-500">{label}</dt>
      <dd className="text-sm text-gray-900 text-right">{value}</dd>
    </div>
  );

  // Status-specific information components
  const DeliveredInfo = () => (
    <InfoCard title="Delivery Information" icon={Mail}>
      <dl className="divide-y divide-gray-200">
        <InfoRow
          label="Delivery Status"
          value={transaction.status === 'relayed' ? 'Successfully Relayed' : 'Successfully Delivered'}
        />
        {transaction.authentication && (
          <>
            <InfoRow
              label="SPF Result"
              value={transaction.authentication.spf?.result || 'N/A'}
            />
            <InfoRow
              label="DKIM Result"
              value={transaction.authentication.dkim?.result || 'N/A'}
            />
            <InfoRow
              label="DMARC Result"
              value={transaction.authentication.dmarc?.result || 'N/A'}
            />
          </>
        )}
        <InfoRow
          label="Processing Time"
          value={formatProcessingTime(transaction.processing_time)}
        />
        {transaction.timestamps?.queue && (
          <InfoRow
            label="Queued At"
            value={formatDate(transaction.timestamps.queue)}
          />
        )}
      </dl>
    </InfoCard>
  );

  const BlockedInfo = () => (
    <InfoCard title="Rejection Details" icon={Shield}>
      <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-red-400 mr-2 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-red-800">Email Blocked</h4>
            <p className="text-sm text-red-700 mt-1">
              {transaction.rejection_reason || 'Email was rejected by the server'}
            </p>
          </div>
        </div>
      </div>
      <dl className="divide-y divide-gray-200">
        {transaction.authentication && (
          <>
            <InfoRow
              label="SPF Check"
              value={
                <span className={transaction.authentication.spf?.result === 'fail' ? 'text-red-600 font-medium' : ''}>
                  {transaction.authentication.spf?.result || 'N/A'}
                </span>
              }
            />
            <InfoRow
              label="DKIM Check"
              value={
                <span className={transaction.authentication.dkim?.result === 'fail' ? 'text-red-600 font-medium' : ''}>
                  {transaction.authentication.dkim?.result || 'N/A'}
                </span>
              }
            />
            <InfoRow
              label="DMARC Check"
              value={
                <span className={transaction.authentication.dmarc?.result === 'fail' ? 'text-red-600 font-medium' : ''}>
                  {transaction.authentication.dmarc?.result || 'N/A'}
                </span>
              }
            />
          </>
        )}
        <InfoRow
          label="Rejection Time"
          value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}
        />
      </dl>
    </InfoCard>
  );

  const FailedInfo = () => (
    <InfoCard title="Failure Details" icon={AlertCircle}>
      <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-red-400 mr-2 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-red-800">Technical Failure</h4>
            <p className="text-sm text-red-700 mt-1">
              {transaction.errors?.length > 0
                ? transaction.errors[0]
                : 'Email processing failed due to technical issues'}
            </p>
          </div>
        </div>
      </div>
      <dl className="divide-y divide-gray-200">
        {transaction.errors?.length > 0 && (
          <InfoRow
            label="Error Count"
            value={transaction.errors.length}
          />
        )}
        <InfoRow
          label="Failure Time"
          value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}
        />
        <InfoRow
          label="Processing Time"
          value={formatProcessingTime(transaction.processing_time)}
        />
      </dl>
    </InfoCard>
  );

  const DisconnectedInfo = () => {
    const getDisconnectionStage = () => {
      const timestamps = transaction.timestamps || {};
      if (!timestamps.helo) return 'During initial connection';
      if (!timestamps.mail_from) return 'After HELO, before MAIL FROM';
      if (!timestamps.data_start) return 'After MAIL FROM, before DATA';
      if (!timestamps.data_complete) return 'During DATA transmission';
      return 'After DATA, before completion';
    };

    return (
      <InfoCard title="Connection Details" icon={Server}>
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800">Connection Lost</h4>
              <p className="text-sm text-yellow-700 mt-1">
                Client disconnected {getDisconnectionStage()}
              </p>
            </div>
          </div>
        </div>
        <dl className="divide-y divide-gray-200">
          <InfoRow
            label="Disconnection Stage"
            value={getDisconnectionStage()}
          />
          <InfoRow
            label="Connection Duration"
            value={formatProcessingTime(transaction.processing_time)}
          />
          <InfoRow
            label="Disconnect Time"
            value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}
          />
          {transaction.timestamps?.connect && (
            <InfoRow
              label="Connected At"
              value={formatDate(transaction.timestamps.connect)}
            />
          )}
        </dl>
      </InfoCard>
    );
  };

  const DeferredInfo = () => (
    <InfoCard title="Deferral Details" icon={Clock}>
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
        <div className="flex">
          <Clock className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-yellow-800">Email Deferred</h4>
            <p className="text-sm text-yellow-700 mt-1">
              {transaction.defer_reason || 'Email delivery was temporarily deferred'}
            </p>
          </div>
        </div>
      </div>
      <dl className="divide-y divide-gray-200">
        <InfoRow
          label="Defer Reason"
          value={transaction.defer_reason || 'N/A'}
        />
        {transaction.recipient && transaction.recipient.length > 0 && (
          <>
            <InfoRow
              label="Recipient"
              value={formatEmail(transaction.recipient[0]?.original)}
            />
            {transaction.recipient[0]?.dsn_code && (
              <InfoRow
                label="DSN Code"
                value={transaction.recipient[0].dsn_code}
              />
            )}
            {transaction.recipient[0]?.dsn_msg && (
              <InfoRow
                label="DSN Message"
                value={transaction.recipient[0].dsn_msg}
              />
            )}
            {transaction.recipient[0]?.dsn_status && (
              <InfoRow
                label="DSN Status"
                value={transaction.recipient[0].dsn_status}
              />
            )}
          </>
        )}
        <InfoRow
          label="Deferral Time"
          value={formatDate(transaction.timestamp)}
        />
        <InfoRow
          label="Processing Time"
          value={formatProcessingTime(transaction.processing_time)}
        />
      </dl>
    </InfoCard>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          to="/transactions"
          className="flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Transactions
        </Link>
      </div>

      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Transaction Details
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Transaction ID: {transaction.transaction_id}
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <span className={`badge ${getStatusBadge(transaction.status, transaction).className} text-base px-3 py-1`}>
            {getStatusBadge(transaction.status, transaction).label}
          </span>
        </div>
      </div>

      {/* Overview */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <InfoCard title="Message Information" icon={Mail}>
            <dl className="divide-y divide-gray-200">
              <InfoRow
                label="From"
                value={
                  transaction.mail_from?.address
                    ? formatEmail(transaction.mail_from.address)
                    : transaction.sender?.original
                      ? formatEmail(transaction.sender.original)
                      : 'N/A'
                }
              />
              <InfoRow
                label="To"
                value={
                  transaction.rcpt_to?.length > 0
                    ? transaction.rcpt_to.map(r => formatEmail(r.address)).join(', ')
                    : transaction.recipient?.length > 0
                      ? transaction.recipient.map(r => formatEmail(r.original)).join(', ')
                      : 'No recipients'
                }
              />
              <InfoRow 
                label="Subject" 
                value={formatSubject(transaction.message?.subject)} 
              />
              <InfoRow 
                label="Message ID" 
                value={transaction.message?.message_id || 'N/A'} 
              />
              <InfoRow 
                label="Size" 
                value={formatFileSize(transaction.message?.size)} 
              />
              <InfoRow 
                label="HELO/EHLO" 
                value={transaction.helo || 'N/A'} 
              />
            </dl>
          </InfoCard>
        </div>

        <div>
          <InfoCard title="Timing Information" icon={Clock}>
            <dl className="divide-y divide-gray-200">
              <InfoRow 
                label="Received" 
                value={formatDate(transaction.timestamp)} 
              />
              <InfoRow 
                label="Relative" 
                value={formatRelativeTime(transaction.timestamp)} 
              />
              <InfoRow 
                label="Processing Time" 
                value={formatProcessingTime(transaction.processing_time)} 
              />
              {transaction.timestamps?.connect && (
                <InfoRow 
                  label="Connected" 
                  value={formatDate(transaction.timestamps.connect, 'HH:mm:ss.SSS')} 
                />
              )}
              {transaction.timestamps?.disconnect && (
                <InfoRow 
                  label="Disconnected" 
                  value={formatDate(transaction.timestamps.disconnect, 'HH:mm:ss.SSS')} 
                />
              )}
            </dl>
          </InfoCard>
        </div>
      </div>

      {/* Connection & Authentication */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <InfoCard title="Connection Information" icon={Server}>
          <dl className="divide-y divide-gray-200">
            <InfoRow 
              label="Remote IP" 
              value={transaction.connection?.remote_ip || 'N/A'} 
            />
            <InfoRow 
              label="Remote Host" 
              value={transaction.connection?.remote_host || 'N/A'} 
            />
            <InfoRow 
              label="Local IP" 
              value={transaction.connection?.local_ip || 'N/A'} 
            />
            <InfoRow 
              label="Local Port" 
              value={transaction.connection?.local_port || 'N/A'} 
            />
            <InfoRow 
              label="TLS Enabled" 
              value={transaction.connection?.tls?.enabled ? 'Yes' : 'No'} 
            />
            {transaction.connection?.tls?.cipher && (
              <InfoRow 
                label="TLS Cipher" 
                value={transaction.connection.tls.cipher} 
              />
            )}
          </dl>
        </InfoCard>

        <InfoCard title="Authentication" icon={Shield}>
          <dl className="divide-y divide-gray-200">
            <InfoRow 
              label="SPF" 
              value={transaction.authentication?.spf || 'N/A'} 
            />
            <InfoRow 
              label="DKIM" 
              value={transaction.authentication?.dkim || 'N/A'} 
            />
            <InfoRow 
              label="DMARC" 
              value={transaction.authentication?.dmarc || 'N/A'} 
            />
          </dl>
        </InfoCard>
      </div>

      {/* Status-specific Information */}
      {consolidatedStatus === 'delivered' && <DeliveredInfo />}
      {consolidatedStatus === 'blocked' && <BlockedInfo />}
      {consolidatedStatus === 'deferred' && <DeferredInfo />}
      {consolidatedStatus === 'failed' && <FailedInfo />}
      {consolidatedStatus === 'disconnected' && <DisconnectedInfo />}

      {/* Headers */}
      {transaction.headers && Object.keys(transaction.headers).length > 0 && (
        <InfoCard title="Email Headers" icon={FileText}>
          <div className="space-y-3">
            {Object.entries(transaction.headers).map(([key, value]) => (
              <div key={key} className="border-b border-gray-200 pb-3 last:border-b-0">
                <dt className="text-sm font-medium text-gray-500 mb-1 capitalize">
                  {key.replace(/-/g, ' ')}
                </dt>
                <dd className="text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded break-all">
                  {formatHeaderValue(value)}
                </dd>
              </div>
            ))}
          </div>
        </InfoCard>
      )}

      {/* Timeline */}
      {transaction.timestamps && (
        <InfoCard title="Transaction Timeline" icon={Calendar}>
          <div className="flow-root">
            <ul className="-mb-8">
              {Object.entries(transaction.timestamps)
                .sort(([,a], [,b]) => new Date(a) - new Date(b))
                .map(([event, timestamp], index, array) => (
                <li key={event}>
                  <div className="relative pb-8">
                    {index !== array.length - 1 && (
                      <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" />
                    )}
                    <div className="relative flex space-x-3">
                      <div>
                        <span className="h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white">
                          <Hash className="h-4 w-4 text-white" />
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p className="text-sm text-gray-500 capitalize">
                            {event.replace(/_/g, ' ')}
                          </p>
                        </div>
                        <div className="text-right text-sm whitespace-nowrap text-gray-500">
                          <time dateTime={timestamp}>
                            {formatDate(timestamp, 'HH:mm:ss.SSS')}
                          </time>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </InfoCard>
      )}

      {/* Errors */}
      {transaction.errors && transaction.errors.length > 0 && (
        <InfoCard title="Errors" icon={AlertCircle}>
          <div className="space-y-2">
            {transaction.errors.map((error, index) => (
              <div key={index} className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            ))}
          </div>
        </InfoCard>
      )}
    </div>
  );
};

export default TransactionDetail;
