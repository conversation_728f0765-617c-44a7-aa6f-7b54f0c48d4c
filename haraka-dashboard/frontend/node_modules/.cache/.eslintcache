[{"/home/<USER>/haraka/haraka-dashboard/frontend/src/index.js": "1", "/home/<USER>/haraka/haraka-dashboard/frontend/src/App.js": "2", "/home/<USER>/haraka/haraka-dashboard/frontend/src/components/Layout.js": "3", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Dashboard.js": "4", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Transactions.js": "5", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/TransactionDetail.js": "6", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Statistics.js": "7", "/home/<USER>/haraka/haraka-dashboard/frontend/src/hooks/useApi.js": "8", "/home/<USER>/haraka/haraka-dashboard/frontend/src/utils/formatters.js": "9", "/home/<USER>/haraka/haraka-dashboard/frontend/src/components/ProtectedRoute.js": "10", "/home/<USER>/haraka/haraka-dashboard/frontend/src/contexts/AuthContext.js": "11", "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Login.js": "12"}, {"size": 254, "mtime": 1753041207794, "results": "13", "hashOfConfig": "14"}, {"size": 1502, "mtime": 1753045159382, "results": "15", "hashOfConfig": "14"}, {"size": 6513, "mtime": 1753045209423, "results": "16", "hashOfConfig": "14"}, {"size": 10851, "mtime": 1753041343965, "results": "17", "hashOfConfig": "14"}, {"size": 18142, "mtime": 1753050218918, "results": "18", "hashOfConfig": "14"}, {"size": 16888, "mtime": 1753050361201, "results": "19", "hashOfConfig": "14"}, {"size": 10652, "mtime": 1753041569425, "results": "20", "hashOfConfig": "14"}, {"size": 5772, "mtime": 1753049046344, "results": "21", "hashOfConfig": "14"}, {"size": 6344, "mtime": 1753050186902, "results": "22", "hashOfConfig": "14"}, {"size": 1072, "mtime": 1753045144106, "results": "23", "hashOfConfig": "14"}, {"size": 2952, "mtime": 1753045105389, "results": "24", "hashOfConfig": "14"}, {"size": 5818, "mtime": 1753045131913, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "esj15b", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/haraka/haraka-dashboard/frontend/src/index.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/App.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/components/Layout.js", ["62", "63"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Dashboard.js", ["64"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Transactions.js", ["65", "66", "67"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/TransactionDetail.js", ["68", "69"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Statistics.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/hooks/useApi.js", ["70", "71", "72"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/utils/formatters.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/components/ProtectedRoute.js", [], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/contexts/AuthContext.js", ["73"], [], "/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Login.js", ["74"], [], {"ruleId": "75", "severity": 1, "message": "76", "line": 10, "column": 3, "nodeType": "77", "messageId": "78", "endLine": 10, "endColumn": 11}, {"ruleId": "75", "severity": 1, "message": "79", "line": 11, "column": 3, "nodeType": "77", "messageId": "78", "endLine": 11, "endColumn": 9}, {"ruleId": "75", "severity": 1, "message": "80", "line": 9, "column": 3, "nodeType": "77", "messageId": "78", "endLine": 9, "endColumn": 14}, {"ruleId": "75", "severity": 1, "message": "81", "line": 9, "column": 3, "nodeType": "77", "messageId": "78", "endLine": 9, "endColumn": 11}, {"ruleId": "75", "severity": 1, "message": "82", "line": 22, "column": 3, "nodeType": "77", "messageId": "78", "endLine": 22, "endColumn": 31}, {"ruleId": "83", "severity": 1, "message": "84", "line": 50, "column": 6, "nodeType": "85", "endLine": 50, "endColumn": 15, "suggestions": "86"}, {"ruleId": "75", "severity": 1, "message": "87", "line": 10, "column": 3, "nodeType": "77", "messageId": "78", "endLine": 10, "endColumn": 7}, {"ruleId": "75", "severity": 1, "message": "88", "line": 11, "column": 3, "nodeType": "77", "messageId": "78", "endLine": 11, "endColumn": 8}, {"ruleId": "83", "severity": 1, "message": "89", "line": 76, "column": 6, "nodeType": "85", "endLine": 76, "endColumn": 41, "suggestions": "90"}, {"ruleId": "83", "severity": 1, "message": "91", "line": 120, "column": 6, "nodeType": "85", "endLine": 120, "endColumn": 36, "suggestions": "92"}, {"ruleId": "83", "severity": 1, "message": "93", "line": 159, "column": 6, "nodeType": "85", "endLine": 159, "endColumn": 47, "suggestions": "94"}, {"ruleId": "83", "severity": 1, "message": "95", "line": 44, "column": 6, "nodeType": "85", "endLine": 44, "endColumn": 13, "suggestions": "96"}, {"ruleId": "75", "severity": 1, "message": "97", "line": 3, "column": 16, "nodeType": "77", "messageId": "78", "endLine": 3, "endColumn": 20}, "no-unused-vars", "'Activity' is defined but never used.", "Identifier", "unusedVar", "'Server' is defined but never used.", "'AlertCircle' is defined but never used.", "'Calendar' is defined but never used.", "'consolidateTransactionStatus' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'refetch'. Either include it or remove the dependency array.", "ArrayExpression", ["98"], "'User' is defined but never used.", "'Globe' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["99"], "React Hook useEffect has a missing dependency: 'fetchTransactions'. Either include it or remove the dependency array.", ["100"], "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", ["101"], "React Hook useEffect has a missing dependency: 'logout'. Either include it or remove the dependency array.", ["102"], "'Lock' is defined but never used.", {"desc": "103", "fix": "104"}, {"desc": "105", "fix": "106"}, {"desc": "107", "fix": "108"}, {"desc": "109", "fix": "110"}, {"desc": "111", "fix": "112"}, "Update the dependencies array to be: [filters, refetch]", {"range": "113", "text": "114"}, "Update the dependencies array to be: [url, isAuthenticated, authLoading, fetchData]", {"range": "115", "text": "116"}, "Update the dependencies array to be: [isAuthenticated, authLoading, fetchTransactions]", {"range": "117", "text": "118"}, "Update the dependencies array to be: [timeframe, isAuthenticated, authLoading, fetchStats]", {"range": "119", "text": "120"}, "Update the dependencies array to be: [logout, token]", {"range": "121", "text": "122"}, [1017, 1026], "[filters, refetch]", [2015, 2050], "[url, isAuthenticated, authLoading, fetchData]", [3353, 3383], "[isAuthenticated, authLoading, fetchTransactions]", [4495, 4536], "[timeframe, isAuthenticated, authLoading, fetchStats]", [1190, 1197], "[logout, token]"]