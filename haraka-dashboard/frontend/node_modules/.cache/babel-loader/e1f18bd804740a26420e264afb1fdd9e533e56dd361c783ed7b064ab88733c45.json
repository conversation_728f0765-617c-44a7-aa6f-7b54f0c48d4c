{"ast": null, "code": "import{format,formatDistanceToNow,isValid}from'date-fns';// Format date for display\nexport const formatDate=function(date){let formatString=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'MMM dd, yyyy HH:mm:ss';if(!date)return'N/A';const dateObj=typeof date==='string'?new Date(date):date;if(!isValid(dateObj))return'Invalid Date';return format(dateObj,formatString);};// Format relative time (e.g., \"2 hours ago\")\nexport const formatRelativeTime=date=>{if(!date)return'N/A';const dateObj=typeof date==='string'?new Date(date):date;if(!isValid(dateObj))return'Invalid Date';return formatDistanceToNow(dateObj,{addSuffix:true});};// Format email address for display\nexport const formatEmail=email=>{if(!email)return'N/A';// Remove angle brackets if present\nreturn email.replace(/[<>]/g,'').trim();};// Format subject line\nexport const formatSubject=subject=>{if(!subject)return'No Subject';// Decode basic MIME encoded subjects\nlet decoded=subject;// Handle =?utf-8?q?...?= encoding\nconst mimeRegex=/=\\?([^?]+)\\?([qb])\\?([^?]*)\\?=/gi;decoded=decoded.replace(mimeRegex,(match,charset,encoding,text)=>{if(encoding.toLowerCase()==='q'){// Quoted-printable decoding (basic)\nreturn text.replace(/=([0-9A-F]{2})/gi,(match,hex)=>{return String.fromCharCode(parseInt(hex,16));}).replace(/_/g,' ');}return text;});return decoded.trim();};// Format file size\nexport const formatFileSize=bytes=>{if(!bytes||bytes===0)return'0 B';const k=1024;const sizes=['B','KB','MB','GB'];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(1))+' '+sizes[i];};// Format processing time\nexport const formatProcessingTime=ms=>{if(!ms&&ms!==0)return'N/A';if(ms<1000){return`${ms}ms`;}else{return`${(ms/1000).toFixed(2)}s`;}};// Format status with appropriate styling\nexport const getStatusBadge=status=>{const statusMap={'delivered':{label:'Delivered',className:'badge-success'},'relayed':{label:'Relayed',className:'badge-info'},'queued':{label:'Queued',className:'badge-warning'},'bounced':{label:'Bounced',className:'badge-danger'},'rejected':{label:'Rejected',className:'badge-danger'},'deferred':{label:'Deferred',className:'badge-warning'},'failed':{label:'Failed',className:'badge-danger'},'accepted':{label:'Accepted',className:'badge-success'},'disconnected':{label:'Disconnected',className:'badge-warning'},'in_progress':{label:'In Progress',className:'badge-info'}};return statusMap[status]||{label:status||'Unknown',className:'badge-info'};};// Format IP address\nexport const formatIP=ip=>{if(!ip)return'N/A';return ip;};// Format hostname\nexport const formatHostname=hostname=>{if(!hostname)return'N/A';return hostname;};// Truncate text with ellipsis\nexport const truncateText=function(text){let maxLength=arguments.length>1&&arguments[1]!==undefined?arguments[1]:50;if(!text)return'';if(text.length<=maxLength)return text;return text.substring(0,maxLength)+'...';};// Format header value for display\nexport const formatHeaderValue=value=>{if(!value)return'N/A';if(Array.isArray(value)){return value.join(', ').trim();}return String(value).trim();};// Extract domain from email\nexport const extractDomain=email=>{if(!email)return'N/A';const cleaned=formatEmail(email);const atIndex=cleaned.lastIndexOf('@');if(atIndex===-1)return'N/A';return cleaned.substring(atIndex+1);};// Format recipient list\nexport const formatRecipients=recipients=>{if(!recipients||!Array.isArray(recipients))return'N/A';if(recipients.length===0)return'No recipients';if(recipients.length===1){return formatEmail(recipients[0].address||recipients[0]);}const first=formatEmail(recipients[0].address||recipients[0]);return`${first} (+${recipients.length-1} more)`;};", "map": {"version": 3, "names": ["format", "formatDistanceToNow", "<PERSON><PERSON><PERSON><PERSON>", "formatDate", "date", "formatString", "arguments", "length", "undefined", "date<PERSON><PERSON>j", "Date", "formatRelativeTime", "addSuffix", "formatEmail", "email", "replace", "trim", "formatSubject", "subject", "decoded", "mimeRegex", "match", "charset", "encoding", "text", "toLowerCase", "hex", "String", "fromCharCode", "parseInt", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "formatProcessingTime", "ms", "getStatusBadge", "status", "statusMap", "label", "className", "formatIP", "ip", "formatHostname", "hostname", "truncateText", "max<PERSON><PERSON><PERSON>", "substring", "formatHeaderValue", "value", "Array", "isArray", "join", "extractDomain", "cleaned", "atIndex", "lastIndexOf", "formatRecipients", "recipients", "address", "first"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/utils/formatters.js"], "sourcesContent": ["import { format, formatDistanceToNow, isValid } from 'date-fns';\n\n// Format date for display\nexport const formatDate = (date, formatString = 'MMM dd, yyyy HH:mm:ss') => {\n  if (!date) return 'N/A';\n  \n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  if (!isValid(dateObj)) return 'Invalid Date';\n  \n  return format(dateObj, formatString);\n};\n\n// Format relative time (e.g., \"2 hours ago\")\nexport const formatRelativeTime = (date) => {\n  if (!date) return 'N/A';\n  \n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  if (!isValid(dateObj)) return 'Invalid Date';\n  \n  return formatDistanceToNow(dateObj, { addSuffix: true });\n};\n\n// Format email address for display\nexport const formatEmail = (email) => {\n  if (!email) return 'N/A';\n  \n  // Remove angle brackets if present\n  return email.replace(/[<>]/g, '').trim();\n};\n\n// Format subject line\nexport const formatSubject = (subject) => {\n  if (!subject) return 'No Subject';\n  \n  // Decode basic MIME encoded subjects\n  let decoded = subject;\n  \n  // Handle =?utf-8?q?...?= encoding\n  const mimeRegex = /=\\?([^?]+)\\?([qb])\\?([^?]*)\\?=/gi;\n  decoded = decoded.replace(mimeRegex, (match, charset, encoding, text) => {\n    if (encoding.toLowerCase() === 'q') {\n      // Quoted-printable decoding (basic)\n      return text.replace(/=([0-9A-F]{2})/gi, (match, hex) => {\n        return String.fromCharCode(parseInt(hex, 16));\n      }).replace(/_/g, ' ');\n    }\n    return text;\n  });\n  \n  return decoded.trim();\n};\n\n// Format file size\nexport const formatFileSize = (bytes) => {\n  if (!bytes || bytes === 0) return '0 B';\n  \n  const k = 1024;\n  const sizes = ['B', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\n};\n\n// Format processing time\nexport const formatProcessingTime = (ms) => {\n  if (!ms && ms !== 0) return 'N/A';\n  \n  if (ms < 1000) {\n    return `${ms}ms`;\n  } else {\n    return `${(ms / 1000).toFixed(2)}s`;\n  }\n};\n\n// Format status with appropriate styling\nexport const getStatusBadge = (status) => {\n  const statusMap = {\n    'delivered': { label: 'Delivered', className: 'badge-success' },\n    'relayed': { label: 'Relayed', className: 'badge-info' },\n    'queued': { label: 'Queued', className: 'badge-warning' },\n    'bounced': { label: 'Bounced', className: 'badge-danger' },\n    'rejected': { label: 'Rejected', className: 'badge-danger' },\n    'deferred': { label: 'Deferred', className: 'badge-warning' },\n    'failed': { label: 'Failed', className: 'badge-danger' },\n    'accepted': { label: 'Accepted', className: 'badge-success' },\n    'disconnected': { label: 'Disconnected', className: 'badge-warning' },\n    'in_progress': { label: 'In Progress', className: 'badge-info' },\n  };\n\n  return statusMap[status] || { label: status || 'Unknown', className: 'badge-info' };\n};\n\n// Format IP address\nexport const formatIP = (ip) => {\n  if (!ip) return 'N/A';\n  return ip;\n};\n\n// Format hostname\nexport const formatHostname = (hostname) => {\n  if (!hostname) return 'N/A';\n  return hostname;\n};\n\n// Truncate text with ellipsis\nexport const truncateText = (text, maxLength = 50) => {\n  if (!text) return '';\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n};\n\n// Format header value for display\nexport const formatHeaderValue = (value) => {\n  if (!value) return 'N/A';\n  \n  if (Array.isArray(value)) {\n    return value.join(', ').trim();\n  }\n  \n  return String(value).trim();\n};\n\n// Extract domain from email\nexport const extractDomain = (email) => {\n  if (!email) return 'N/A';\n  \n  const cleaned = formatEmail(email);\n  const atIndex = cleaned.lastIndexOf('@');\n  \n  if (atIndex === -1) return 'N/A';\n  \n  return cleaned.substring(atIndex + 1);\n};\n\n// Format recipient list\nexport const formatRecipients = (recipients) => {\n  if (!recipients || !Array.isArray(recipients)) return 'N/A';\n  \n  if (recipients.length === 0) return 'No recipients';\n  \n  if (recipients.length === 1) {\n    return formatEmail(recipients[0].address || recipients[0]);\n  }\n  \n  const first = formatEmail(recipients[0].address || recipients[0]);\n  return `${first} (+${recipients.length - 1} more)`;\n};\n"], "mappings": "AAAA,OAASA,MAAM,CAAEC,mBAAmB,CAAEC,OAAO,KAAQ,UAAU,CAE/D;AACA,MAAO,MAAM,CAAAC,UAAU,CAAG,QAAAA,CAACC,IAAI,CAA6C,IAA3C,CAAAC,YAAY,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,uBAAuB,CACrE,GAAI,CAACF,IAAI,CAAE,MAAO,KAAK,CAEvB,KAAM,CAAAK,OAAO,CAAG,MAAO,CAAAL,IAAI,GAAK,QAAQ,CAAG,GAAI,CAAAM,IAAI,CAACN,IAAI,CAAC,CAAGA,IAAI,CAEhE,GAAI,CAACF,OAAO,CAACO,OAAO,CAAC,CAAE,MAAO,cAAc,CAE5C,MAAO,CAAAT,MAAM,CAACS,OAAO,CAAEJ,YAAY,CAAC,CACtC,CAAC,CAED;AACA,MAAO,MAAM,CAAAM,kBAAkB,CAAIP,IAAI,EAAK,CAC1C,GAAI,CAACA,IAAI,CAAE,MAAO,KAAK,CAEvB,KAAM,CAAAK,OAAO,CAAG,MAAO,CAAAL,IAAI,GAAK,QAAQ,CAAG,GAAI,CAAAM,IAAI,CAACN,IAAI,CAAC,CAAGA,IAAI,CAEhE,GAAI,CAACF,OAAO,CAACO,OAAO,CAAC,CAAE,MAAO,cAAc,CAE5C,MAAO,CAAAR,mBAAmB,CAACQ,OAAO,CAAE,CAAEG,SAAS,CAAE,IAAK,CAAC,CAAC,CAC1D,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,WAAW,CAAIC,KAAK,EAAK,CACpC,GAAI,CAACA,KAAK,CAAE,MAAO,KAAK,CAExB;AACA,MAAO,CAAAA,KAAK,CAACC,OAAO,CAAC,OAAO,CAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAC1C,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,aAAa,CAAIC,OAAO,EAAK,CACxC,GAAI,CAACA,OAAO,CAAE,MAAO,YAAY,CAEjC;AACA,GAAI,CAAAC,OAAO,CAAGD,OAAO,CAErB;AACA,KAAM,CAAAE,SAAS,CAAG,kCAAkC,CACpDD,OAAO,CAAGA,OAAO,CAACJ,OAAO,CAACK,SAAS,CAAE,CAACC,KAAK,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,IAAI,GAAK,CACvE,GAAID,QAAQ,CAACE,WAAW,CAAC,CAAC,GAAK,GAAG,CAAE,CAClC;AACA,MAAO,CAAAD,IAAI,CAACT,OAAO,CAAC,kBAAkB,CAAE,CAACM,KAAK,CAAEK,GAAG,GAAK,CACtD,MAAO,CAAAC,MAAM,CAACC,YAAY,CAACC,QAAQ,CAACH,GAAG,CAAE,EAAE,CAAC,CAAC,CAC/C,CAAC,CAAC,CAACX,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CACvB,CACA,MAAO,CAAAS,IAAI,CACb,CAAC,CAAC,CAEF,MAAO,CAAAL,OAAO,CAACH,IAAI,CAAC,CAAC,CACvB,CAAC,CAED;AACA,MAAO,MAAM,CAAAc,cAAc,CAAIC,KAAK,EAAK,CACvC,GAAI,CAACA,KAAK,EAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,KAAK,CAEvC,KAAM,CAAAC,CAAC,CAAG,IAAI,CACd,KAAM,CAAAC,KAAK,CAAG,CAAC,GAAG,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACrC,KAAM,CAAAC,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,CAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC,CAEnD,MAAO,CAAAM,UAAU,CAAC,CAACP,KAAK,CAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,CAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAG,GAAG,CAAGP,KAAK,CAACC,CAAC,CAAC,CACzE,CAAC,CAED;AACA,MAAO,MAAM,CAAAO,oBAAoB,CAAIC,EAAE,EAAK,CAC1C,GAAI,CAACA,EAAE,EAAIA,EAAE,GAAK,CAAC,CAAE,MAAO,KAAK,CAEjC,GAAIA,EAAE,CAAG,IAAI,CAAE,CACb,MAAO,GAAGA,EAAE,IAAI,CAClB,CAAC,IAAM,CACL,MAAO,GAAG,CAACA,EAAE,CAAG,IAAI,EAAEF,OAAO,CAAC,CAAC,CAAC,GAAG,CACrC,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAG,cAAc,CAAIC,MAAM,EAAK,CACxC,KAAM,CAAAC,SAAS,CAAG,CAChB,WAAW,CAAE,CAAEC,KAAK,CAAE,WAAW,CAAEC,SAAS,CAAE,eAAgB,CAAC,CAC/D,SAAS,CAAE,CAAED,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,YAAa,CAAC,CACxD,QAAQ,CAAE,CAAED,KAAK,CAAE,QAAQ,CAAEC,SAAS,CAAE,eAAgB,CAAC,CACzD,SAAS,CAAE,CAAED,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,cAAe,CAAC,CAC1D,UAAU,CAAE,CAAED,KAAK,CAAE,UAAU,CAAEC,SAAS,CAAE,cAAe,CAAC,CAC5D,UAAU,CAAE,CAAED,KAAK,CAAE,UAAU,CAAEC,SAAS,CAAE,eAAgB,CAAC,CAC7D,QAAQ,CAAE,CAAED,KAAK,CAAE,QAAQ,CAAEC,SAAS,CAAE,cAAe,CAAC,CACxD,UAAU,CAAE,CAAED,KAAK,CAAE,UAAU,CAAEC,SAAS,CAAE,eAAgB,CAAC,CAC7D,cAAc,CAAE,CAAED,KAAK,CAAE,cAAc,CAAEC,SAAS,CAAE,eAAgB,CAAC,CACrE,aAAa,CAAE,CAAED,KAAK,CAAE,aAAa,CAAEC,SAAS,CAAE,YAAa,CACjE,CAAC,CAED,MAAO,CAAAF,SAAS,CAACD,MAAM,CAAC,EAAI,CAAEE,KAAK,CAAEF,MAAM,EAAI,SAAS,CAAEG,SAAS,CAAE,YAAa,CAAC,CACrF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,QAAQ,CAAIC,EAAE,EAAK,CAC9B,GAAI,CAACA,EAAE,CAAE,MAAO,KAAK,CACrB,MAAO,CAAAA,EAAE,CACX,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,cAAc,CAAIC,QAAQ,EAAK,CAC1C,GAAI,CAACA,QAAQ,CAAE,MAAO,KAAK,CAC3B,MAAO,CAAAA,QAAQ,CACjB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,YAAY,CAAG,QAAAA,CAAC5B,IAAI,CAAqB,IAAnB,CAAA6B,SAAS,CAAA/C,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC/C,GAAI,CAACkB,IAAI,CAAE,MAAO,EAAE,CACpB,GAAIA,IAAI,CAACjB,MAAM,EAAI8C,SAAS,CAAE,MAAO,CAAA7B,IAAI,CACzC,MAAO,CAAAA,IAAI,CAAC8B,SAAS,CAAC,CAAC,CAAED,SAAS,CAAC,CAAG,KAAK,CAC7C,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,iBAAiB,CAAIC,KAAK,EAAK,CAC1C,GAAI,CAACA,KAAK,CAAE,MAAO,KAAK,CAExB,GAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,CAAE,CACxB,MAAO,CAAAA,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC3C,IAAI,CAAC,CAAC,CAChC,CAEA,MAAO,CAAAW,MAAM,CAAC6B,KAAK,CAAC,CAACxC,IAAI,CAAC,CAAC,CAC7B,CAAC,CAED;AACA,MAAO,MAAM,CAAA4C,aAAa,CAAI9C,KAAK,EAAK,CACtC,GAAI,CAACA,KAAK,CAAE,MAAO,KAAK,CAExB,KAAM,CAAA+C,OAAO,CAAGhD,WAAW,CAACC,KAAK,CAAC,CAClC,KAAM,CAAAgD,OAAO,CAAGD,OAAO,CAACE,WAAW,CAAC,GAAG,CAAC,CAExC,GAAID,OAAO,GAAK,CAAC,CAAC,CAAE,MAAO,KAAK,CAEhC,MAAO,CAAAD,OAAO,CAACP,SAAS,CAACQ,OAAO,CAAG,CAAC,CAAC,CACvC,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,gBAAgB,CAAIC,UAAU,EAAK,CAC9C,GAAI,CAACA,UAAU,EAAI,CAACR,KAAK,CAACC,OAAO,CAACO,UAAU,CAAC,CAAE,MAAO,KAAK,CAE3D,GAAIA,UAAU,CAAC1D,MAAM,GAAK,CAAC,CAAE,MAAO,eAAe,CAEnD,GAAI0D,UAAU,CAAC1D,MAAM,GAAK,CAAC,CAAE,CAC3B,MAAO,CAAAM,WAAW,CAACoD,UAAU,CAAC,CAAC,CAAC,CAACC,OAAO,EAAID,UAAU,CAAC,CAAC,CAAC,CAAC,CAC5D,CAEA,KAAM,CAAAE,KAAK,CAAGtD,WAAW,CAACoD,UAAU,CAAC,CAAC,CAAC,CAACC,OAAO,EAAID,UAAU,CAAC,CAAC,CAAC,CAAC,CACjE,MAAO,GAAGE,KAAK,MAAMF,UAAU,CAAC1D,MAAM,CAAG,CAAC,QAAQ,CACpD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}