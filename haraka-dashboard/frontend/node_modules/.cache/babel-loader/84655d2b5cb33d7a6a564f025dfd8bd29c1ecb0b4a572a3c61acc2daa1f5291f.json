{"ast": null, "code": "import React from'react';import{useParams,Link}from'react-router-dom';import{ArrowLeft,Mail,Clock,Server,Shield,FileText,User,Globe,Calendar,Hash,AlertCircle}from'lucide-react';import{useTransaction}from'../hooks/useApi';import{formatDate,formatRelativeTime,getStatusBadge,consolidateTransactionStatus,formatEmail,formatSubject,formatFileSize,formatProcessingTime,formatHeaderValue}from'../utils/formatters';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const TransactionDetail=()=>{var _transaction$mail_fro,_transaction$rcpt_to,_transaction$message,_transaction$message2,_transaction$message3,_transaction$timestam6,_transaction$timestam7,_transaction$connecti,_transaction$connecti2,_transaction$connecti3,_transaction$connecti4,_transaction$connecti5,_transaction$connecti6,_transaction$connecti7,_transaction$connecti8,_transaction$authenti0,_transaction$authenti1,_transaction$authenti10;const{id}=useParams();const{data:transaction,loading,error}=useTransaction(id);// Get consolidated status for conditional rendering\nconst consolidatedStatus=transaction?consolidateTransactionStatus(transaction):null;if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner mx-auto\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-4 text-sm text-gray-500\",children:\"Loading transaction details...\"})]});}if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"mx-auto h-12 w-12 text-red-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"Error loading transaction\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:error}),/*#__PURE__*/_jsx(Link,{to:\"/transactions\",className:\"mt-4 btn-primary\",children:\"Back to Transactions\"})]});}if(!transaction){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(Mail,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"Transaction not found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"The requested transaction could not be found.\"}),/*#__PURE__*/_jsx(Link,{to:\"/transactions\",className:\"mt-4 btn-primary\",children:\"Back to Transactions\"})]});}const InfoCard=_ref=>{let{title,icon:Icon,children}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"card p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(Icon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:title})]}),children]});};const InfoRow=_ref2=>{let{label,value,className=''}=_ref2;return/*#__PURE__*/_jsxs(\"div\",{className:`flex justify-between py-2 ${className}`,children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500\",children:label}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-sm text-gray-900 text-right\",children:value})]});};// Status-specific information components\nconst DeliveredInfo=()=>{var _transaction$authenti,_transaction$authenti2,_transaction$authenti3,_transaction$timestam;return/*#__PURE__*/_jsx(InfoCard,{title:\"Delivery Information\",icon:Mail,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Delivery Status\",value:transaction.status==='relayed'?'Successfully Relayed':'Successfully Delivered'}),transaction.authentication&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(InfoRow,{label:\"SPF Result\",value:((_transaction$authenti=transaction.authentication.spf)===null||_transaction$authenti===void 0?void 0:_transaction$authenti.result)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DKIM Result\",value:((_transaction$authenti2=transaction.authentication.dkim)===null||_transaction$authenti2===void 0?void 0:_transaction$authenti2.result)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DMARC Result\",value:((_transaction$authenti3=transaction.authentication.dmarc)===null||_transaction$authenti3===void 0?void 0:_transaction$authenti3.result)||'N/A'})]}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)}),((_transaction$timestam=transaction.timestamps)===null||_transaction$timestam===void 0?void 0:_transaction$timestam.queue)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Queued At\",value:formatDate(transaction.timestamps.queue)})]})});};const BlockedInfo=()=>{var _transaction$authenti4,_transaction$authenti5,_transaction$authenti6,_transaction$authenti7,_transaction$authenti8,_transaction$authenti9,_transaction$timestam2;return/*#__PURE__*/_jsxs(InfoCard,{title:\"Rejection Details\",icon:Shield,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"h-5 w-5 text-red-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-red-800\",children:\"Email Blocked\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-700 mt-1\",children:transaction.rejection_reason||'Email was rejected by the server'})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[transaction.authentication&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(InfoRow,{label:\"SPF Check\",value:/*#__PURE__*/_jsx(\"span\",{className:((_transaction$authenti4=transaction.authentication.spf)===null||_transaction$authenti4===void 0?void 0:_transaction$authenti4.result)==='fail'?'text-red-600 font-medium':'',children:((_transaction$authenti5=transaction.authentication.spf)===null||_transaction$authenti5===void 0?void 0:_transaction$authenti5.result)||'N/A'})}),/*#__PURE__*/_jsx(InfoRow,{label:\"DKIM Check\",value:/*#__PURE__*/_jsx(\"span\",{className:((_transaction$authenti6=transaction.authentication.dkim)===null||_transaction$authenti6===void 0?void 0:_transaction$authenti6.result)==='fail'?'text-red-600 font-medium':'',children:((_transaction$authenti7=transaction.authentication.dkim)===null||_transaction$authenti7===void 0?void 0:_transaction$authenti7.result)||'N/A'})}),/*#__PURE__*/_jsx(InfoRow,{label:\"DMARC Check\",value:/*#__PURE__*/_jsx(\"span\",{className:((_transaction$authenti8=transaction.authentication.dmarc)===null||_transaction$authenti8===void 0?void 0:_transaction$authenti8.result)==='fail'?'text-red-600 font-medium':'',children:((_transaction$authenti9=transaction.authentication.dmarc)===null||_transaction$authenti9===void 0?void 0:_transaction$authenti9.result)||'N/A'})})]}),/*#__PURE__*/_jsx(InfoRow,{label:\"Rejection Time\",value:formatDate(((_transaction$timestam2=transaction.timestamps)===null||_transaction$timestam2===void 0?void 0:_transaction$timestam2.disconnect)||transaction.timestamp)})]})]});};const FailedInfo=()=>{var _transaction$errors,_transaction$errors2,_transaction$timestam3;return/*#__PURE__*/_jsxs(InfoCard,{title:\"Failure Details\",icon:AlertCircle,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"h-5 w-5 text-red-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-red-800\",children:\"Technical Failure\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-700 mt-1\",children:((_transaction$errors=transaction.errors)===null||_transaction$errors===void 0?void 0:_transaction$errors.length)>0?transaction.errors[0]:'Email processing failed due to technical issues'})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[((_transaction$errors2=transaction.errors)===null||_transaction$errors2===void 0?void 0:_transaction$errors2.length)>0&&/*#__PURE__*/_jsx(InfoRow,{label:\"Error Count\",value:transaction.errors.length}),/*#__PURE__*/_jsx(InfoRow,{label:\"Failure Time\",value:formatDate(((_transaction$timestam3=transaction.timestamps)===null||_transaction$timestam3===void 0?void 0:_transaction$timestam3.disconnect)||transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)})]})]});};const DisconnectedInfo=()=>{var _transaction$timestam4,_transaction$timestam5;const getDisconnectionStage=()=>{const timestamps=transaction.timestamps||{};if(!timestamps.helo)return'During initial connection';if(!timestamps.mail_from)return'After HELO, before MAIL FROM';if(!timestamps.data_start)return'After MAIL FROM, before DATA';if(!timestamps.data_complete)return'During DATA transmission';return'After DATA, before completion';};return/*#__PURE__*/_jsxs(InfoCard,{title:\"Connection Details\",icon:Server,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-yellow-800\",children:\"Connection Lost\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-yellow-700 mt-1\",children:[\"Client disconnected \",getDisconnectionStage()]})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Disconnection Stage\",value:getDisconnectionStage()}),/*#__PURE__*/_jsx(InfoRow,{label:\"Connection Duration\",value:formatProcessingTime(transaction.processing_time)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Disconnect Time\",value:formatDate(((_transaction$timestam4=transaction.timestamps)===null||_transaction$timestam4===void 0?void 0:_transaction$timestam4.disconnect)||transaction.timestamp)}),((_transaction$timestam5=transaction.timestamps)===null||_transaction$timestam5===void 0?void 0:_transaction$timestam5.connect)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Connected At\",value:formatDate(transaction.timestamps.connect)})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-4\",children:/*#__PURE__*/_jsxs(Link,{to:\"/transactions\",className:\"flex items-center text-sm font-medium text-gray-500 hover:text-gray-700\",children:[/*#__PURE__*/_jsx(ArrowLeft,{className:\"h-4 w-4 mr-1\"}),\"Back to Transactions\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:flex md:items-center md:justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\",children:\"Transaction Details\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:[\"Transaction ID: \",transaction.transaction_id]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 flex md:mt-0 md:ml-4\",children:/*#__PURE__*/_jsx(\"span\",{className:`badge ${getStatusBadge(transaction.status,transaction).className} text-base px-3 py-1`,children:getStatusBadge(transaction.status,transaction).label})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6 lg:grid-cols-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-2\",children:/*#__PURE__*/_jsx(InfoCard,{title:\"Message Information\",icon:Mail,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"From\",value:formatEmail((_transaction$mail_fro=transaction.mail_from)===null||_transaction$mail_fro===void 0?void 0:_transaction$mail_fro.address)}),/*#__PURE__*/_jsx(InfoRow,{label:\"To\",value:((_transaction$rcpt_to=transaction.rcpt_to)===null||_transaction$rcpt_to===void 0?void 0:_transaction$rcpt_to.length)>0?transaction.rcpt_to.map(r=>formatEmail(r.address)).join(', '):'No recipients'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Subject\",value:formatSubject((_transaction$message=transaction.message)===null||_transaction$message===void 0?void 0:_transaction$message.subject)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Message ID\",value:((_transaction$message2=transaction.message)===null||_transaction$message2===void 0?void 0:_transaction$message2.message_id)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Size\",value:formatFileSize((_transaction$message3=transaction.message)===null||_transaction$message3===void 0?void 0:_transaction$message3.size)}),/*#__PURE__*/_jsx(InfoRow,{label:\"HELO/EHLO\",value:transaction.helo||'N/A'})]})})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(InfoCard,{title:\"Timing Information\",icon:Clock,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Received\",value:formatDate(transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Relative\",value:formatRelativeTime(transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)}),((_transaction$timestam6=transaction.timestamps)===null||_transaction$timestam6===void 0?void 0:_transaction$timestam6.connect)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Connected\",value:formatDate(transaction.timestamps.connect,'HH:mm:ss.SSS')}),((_transaction$timestam7=transaction.timestamps)===null||_transaction$timestam7===void 0?void 0:_transaction$timestam7.disconnect)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Disconnected\",value:formatDate(transaction.timestamps.disconnect,'HH:mm:ss.SSS')})]})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6 lg:grid-cols-2\",children:[/*#__PURE__*/_jsx(InfoCard,{title:\"Connection Information\",icon:Server,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Remote IP\",value:((_transaction$connecti=transaction.connection)===null||_transaction$connecti===void 0?void 0:_transaction$connecti.remote_ip)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Remote Host\",value:((_transaction$connecti2=transaction.connection)===null||_transaction$connecti2===void 0?void 0:_transaction$connecti2.remote_host)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Local IP\",value:((_transaction$connecti3=transaction.connection)===null||_transaction$connecti3===void 0?void 0:_transaction$connecti3.local_ip)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Local Port\",value:((_transaction$connecti4=transaction.connection)===null||_transaction$connecti4===void 0?void 0:_transaction$connecti4.local_port)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"TLS Enabled\",value:(_transaction$connecti5=transaction.connection)!==null&&_transaction$connecti5!==void 0&&(_transaction$connecti6=_transaction$connecti5.tls)!==null&&_transaction$connecti6!==void 0&&_transaction$connecti6.enabled?'Yes':'No'}),((_transaction$connecti7=transaction.connection)===null||_transaction$connecti7===void 0?void 0:(_transaction$connecti8=_transaction$connecti7.tls)===null||_transaction$connecti8===void 0?void 0:_transaction$connecti8.cipher)&&/*#__PURE__*/_jsx(InfoRow,{label:\"TLS Cipher\",value:transaction.connection.tls.cipher})]})}),/*#__PURE__*/_jsx(InfoCard,{title:\"Authentication\",icon:Shield,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"SPF\",value:((_transaction$authenti0=transaction.authentication)===null||_transaction$authenti0===void 0?void 0:_transaction$authenti0.spf)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DKIM\",value:((_transaction$authenti1=transaction.authentication)===null||_transaction$authenti1===void 0?void 0:_transaction$authenti1.dkim)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DMARC\",value:((_transaction$authenti10=transaction.authentication)===null||_transaction$authenti10===void 0?void 0:_transaction$authenti10.dmarc)||'N/A'})]})})]}),consolidatedStatus==='delivered'&&/*#__PURE__*/_jsx(DeliveredInfo,{}),consolidatedStatus==='blocked'&&/*#__PURE__*/_jsx(BlockedInfo,{}),consolidatedStatus==='failed'&&/*#__PURE__*/_jsx(FailedInfo,{}),consolidatedStatus==='disconnected'&&/*#__PURE__*/_jsx(DisconnectedInfo,{}),transaction.headers&&Object.keys(transaction.headers).length>0&&/*#__PURE__*/_jsx(InfoCard,{title:\"Email Headers\",icon:FileText,children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:Object.entries(transaction.headers).map(_ref3=>{let[key,value]=_ref3;return/*#__PURE__*/_jsxs(\"div\",{className:\"border-b border-gray-200 pb-3 last:border-b-0\",children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 mb-1 capitalize\",children:key.replace(/-/g,' ')}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded break-all\",children:formatHeaderValue(value)})]},key);})})}),transaction.timestamps&&/*#__PURE__*/_jsx(InfoCard,{title:\"Transaction Timeline\",icon:Calendar,children:/*#__PURE__*/_jsx(\"div\",{className:\"flow-root\",children:/*#__PURE__*/_jsx(\"ul\",{className:\"-mb-8\",children:Object.entries(transaction.timestamps).sort((_ref4,_ref5)=>{let[,a]=_ref4;let[,b]=_ref5;return new Date(a)-new Date(b);}).map((_ref6,index,array)=>{let[event,timestamp]=_ref6;return/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative pb-8\",children:[index!==array.length-1&&/*#__PURE__*/_jsx(\"span\",{className:\"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative flex space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white\",children:/*#__PURE__*/_jsx(Hash,{className:\"h-4 w-4 text-white\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 capitalize\",children:event.replace(/_/g,' ')})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-right text-sm whitespace-nowrap text-gray-500\",children:/*#__PURE__*/_jsx(\"time\",{dateTime:timestamp,children:formatDate(timestamp,'HH:mm:ss.SSS')})})]})]})]})},event);})})})}),transaction.errors&&transaction.errors.length>0&&/*#__PURE__*/_jsx(InfoCard,{title:\"Errors\",icon:AlertCircle,children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:transaction.errors.map((error,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-3\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-800\",children:error})},index))})})]});};export default TransactionDetail;", "map": {"version": 3, "names": ["React", "useParams", "Link", "ArrowLeft", "Mail", "Clock", "Server", "Shield", "FileText", "User", "Globe", "Calendar", "Hash", "AlertCircle", "useTransaction", "formatDate", "formatRelativeTime", "getStatusBadge", "consolidateTransactionStatus", "formatEmail", "formatSubject", "formatFileSize", "formatProcessingTime", "formatHeaderValue", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "TransactionDetail", "_transaction$mail_fro", "_transaction$rcpt_to", "_transaction$message", "_transaction$message2", "_transaction$message3", "_transaction$timestam6", "_transaction$timestam7", "_transaction$connecti", "_transaction$connecti2", "_transaction$connecti3", "_transaction$connecti4", "_transaction$connecti5", "_transaction$connecti6", "_transaction$connecti7", "_transaction$connecti8", "_transaction$authenti0", "_transaction$authenti1", "_transaction$authenti10", "id", "data", "transaction", "loading", "error", "consolidatedStatus", "className", "children", "to", "InfoCard", "_ref", "title", "icon", "Icon", "InfoRow", "_ref2", "label", "value", "DeliveredInfo", "_transaction$authenti", "_transaction$authenti2", "_transaction$authenti3", "_transaction$timestam", "status", "authentication", "spf", "result", "dkim", "dmarc", "processing_time", "timestamps", "queue", "BlockedInfo", "_transaction$authenti4", "_transaction$authenti5", "_transaction$authenti6", "_transaction$authenti7", "_transaction$authenti8", "_transaction$authenti9", "_transaction$timestam2", "rejection_reason", "disconnect", "timestamp", "FailedInfo", "_transaction$errors", "_transaction$errors2", "_transaction$timestam3", "errors", "length", "DisconnectedInfo", "_transaction$timestam4", "_transaction$timestam5", "getDisconnectionStage", "helo", "mail_from", "data_start", "data_complete", "connect", "transaction_id", "address", "rcpt_to", "map", "r", "join", "message", "subject", "message_id", "size", "connection", "remote_ip", "remote_host", "local_ip", "local_port", "tls", "enabled", "cipher", "headers", "Object", "keys", "entries", "_ref3", "key", "replace", "sort", "_ref4", "_ref5", "a", "b", "Date", "_ref6", "index", "array", "event", "dateTime"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/TransactionDetail.js"], "sourcesContent": ["import React from 'react';\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\nimport { \n  ArrowLeft, \n  Mail, \n  Clock, \n  Server, \n  Shield, \n  FileText,\n  User,\n  Globe,\n  Calendar,\n  Hash,\n  AlertCircle\n} from 'lucide-react';\nimport { useTransaction } from '../hooks/useApi';\nimport {\n  formatDate,\n  formatRelativeTime,\n  getStatusBadge,\n  consolidateTransactionStatus,\n  formatEmail,\n  formatSubject,\n  formatFileSize,\n  formatProcessingTime,\n  formatHeaderValue\n} from '../utils/formatters';\n\nconst TransactionDetail = () => {\n  const { id } = useParams();\n  const { data: transaction, loading, error } = useTransaction(id);\n\n  // Get consolidated status for conditional rendering\n  const consolidatedStatus = transaction ? consolidateTransactionStatus(transaction) : null;\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"loading-spinner mx-auto\"></div>\n        <p className=\"mt-4 text-sm text-gray-500\">Loading transaction details...</p>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <AlertCircle className=\"mx-auto h-12 w-12 text-red-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Error loading transaction</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">{error}</p>\n        <Link to=\"/transactions\" className=\"mt-4 btn-primary\">\n          Back to Transactions\n        </Link>\n      </div>\n    );\n  }\n\n  if (!transaction) {\n    return (\n      <div className=\"text-center py-12\">\n        <Mail className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Transaction not found</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">The requested transaction could not be found.</p>\n        <Link to=\"/transactions\" className=\"mt-4 btn-primary\">\n          Back to Transactions\n        </Link>\n      </div>\n    );\n  }\n\n  const InfoCard = ({ title, icon: Icon, children }) => (\n    <div className=\"card p-6\">\n      <div className=\"flex items-center mb-4\">\n        <Icon className=\"h-5 w-5 text-gray-400 mr-2\" />\n        <h3 className=\"text-lg font-medium text-gray-900\">{title}</h3>\n      </div>\n      {children}\n    </div>\n  );\n\n  const InfoRow = ({ label, value, className = '' }) => (\n    <div className={`flex justify-between py-2 ${className}`}>\n      <dt className=\"text-sm font-medium text-gray-500\">{label}</dt>\n      <dd className=\"text-sm text-gray-900 text-right\">{value}</dd>\n    </div>\n  );\n\n  // Status-specific information components\n  const DeliveredInfo = () => (\n    <InfoCard title=\"Delivery Information\" icon={Mail}>\n      <dl className=\"divide-y divide-gray-200\">\n        <InfoRow\n          label=\"Delivery Status\"\n          value={transaction.status === 'relayed' ? 'Successfully Relayed' : 'Successfully Delivered'}\n        />\n        {transaction.authentication && (\n          <>\n            <InfoRow\n              label=\"SPF Result\"\n              value={transaction.authentication.spf?.result || 'N/A'}\n            />\n            <InfoRow\n              label=\"DKIM Result\"\n              value={transaction.authentication.dkim?.result || 'N/A'}\n            />\n            <InfoRow\n              label=\"DMARC Result\"\n              value={transaction.authentication.dmarc?.result || 'N/A'}\n            />\n          </>\n        )}\n        <InfoRow\n          label=\"Processing Time\"\n          value={formatProcessingTime(transaction.processing_time)}\n        />\n        {transaction.timestamps?.queue && (\n          <InfoRow\n            label=\"Queued At\"\n            value={formatDate(transaction.timestamps.queue)}\n          />\n        )}\n      </dl>\n    </InfoCard>\n  );\n\n  const BlockedInfo = () => (\n    <InfoCard title=\"Rejection Details\" icon={Shield}>\n      <div className=\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\">\n        <div className=\"flex\">\n          <AlertCircle className=\"h-5 w-5 text-red-400 mr-2 mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-red-800\">Email Blocked</h4>\n            <p className=\"text-sm text-red-700 mt-1\">\n              {transaction.rejection_reason || 'Email was rejected by the server'}\n            </p>\n          </div>\n        </div>\n      </div>\n      <dl className=\"divide-y divide-gray-200\">\n        {transaction.authentication && (\n          <>\n            <InfoRow\n              label=\"SPF Check\"\n              value={\n                <span className={transaction.authentication.spf?.result === 'fail' ? 'text-red-600 font-medium' : ''}>\n                  {transaction.authentication.spf?.result || 'N/A'}\n                </span>\n              }\n            />\n            <InfoRow\n              label=\"DKIM Check\"\n              value={\n                <span className={transaction.authentication.dkim?.result === 'fail' ? 'text-red-600 font-medium' : ''}>\n                  {transaction.authentication.dkim?.result || 'N/A'}\n                </span>\n              }\n            />\n            <InfoRow\n              label=\"DMARC Check\"\n              value={\n                <span className={transaction.authentication.dmarc?.result === 'fail' ? 'text-red-600 font-medium' : ''}>\n                  {transaction.authentication.dmarc?.result || 'N/A'}\n                </span>\n              }\n            />\n          </>\n        )}\n        <InfoRow\n          label=\"Rejection Time\"\n          value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}\n        />\n      </dl>\n    </InfoCard>\n  );\n\n  const FailedInfo = () => (\n    <InfoCard title=\"Failure Details\" icon={AlertCircle}>\n      <div className=\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\">\n        <div className=\"flex\">\n          <AlertCircle className=\"h-5 w-5 text-red-400 mr-2 mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-red-800\">Technical Failure</h4>\n            <p className=\"text-sm text-red-700 mt-1\">\n              {transaction.errors?.length > 0\n                ? transaction.errors[0]\n                : 'Email processing failed due to technical issues'}\n            </p>\n          </div>\n        </div>\n      </div>\n      <dl className=\"divide-y divide-gray-200\">\n        {transaction.errors?.length > 0 && (\n          <InfoRow\n            label=\"Error Count\"\n            value={transaction.errors.length}\n          />\n        )}\n        <InfoRow\n          label=\"Failure Time\"\n          value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}\n        />\n        <InfoRow\n          label=\"Processing Time\"\n          value={formatProcessingTime(transaction.processing_time)}\n        />\n      </dl>\n    </InfoCard>\n  );\n\n  const DisconnectedInfo = () => {\n    const getDisconnectionStage = () => {\n      const timestamps = transaction.timestamps || {};\n      if (!timestamps.helo) return 'During initial connection';\n      if (!timestamps.mail_from) return 'After HELO, before MAIL FROM';\n      if (!timestamps.data_start) return 'After MAIL FROM, before DATA';\n      if (!timestamps.data_complete) return 'During DATA transmission';\n      return 'After DATA, before completion';\n    };\n\n    return (\n      <InfoCard title=\"Connection Details\" icon={Server}>\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\" />\n            <div>\n              <h4 className=\"text-sm font-medium text-yellow-800\">Connection Lost</h4>\n              <p className=\"text-sm text-yellow-700 mt-1\">\n                Client disconnected {getDisconnectionStage()}\n              </p>\n            </div>\n          </div>\n        </div>\n        <dl className=\"divide-y divide-gray-200\">\n          <InfoRow\n            label=\"Disconnection Stage\"\n            value={getDisconnectionStage()}\n          />\n          <InfoRow\n            label=\"Connection Duration\"\n            value={formatProcessingTime(transaction.processing_time)}\n          />\n          <InfoRow\n            label=\"Disconnect Time\"\n            value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}\n          />\n          {transaction.timestamps?.connect && (\n            <InfoRow\n              label=\"Connected At\"\n              value={formatDate(transaction.timestamps.connect)}\n            />\n          )}\n        </dl>\n      </InfoCard>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4\">\n        <Link\n          to=\"/transactions\"\n          className=\"flex items-center text-sm font-medium text-gray-500 hover:text-gray-700\"\n        >\n          <ArrowLeft className=\"h-4 w-4 mr-1\" />\n          Back to Transactions\n        </Link>\n      </div>\n\n      <div className=\"md:flex md:items-center md:justify-between\">\n        <div className=\"flex-1 min-w-0\">\n          <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n            Transaction Details\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Transaction ID: {transaction.transaction_id}\n          </p>\n        </div>\n        <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n          <span className={`badge ${getStatusBadge(transaction.status, transaction).className} text-base px-3 py-1`}>\n            {getStatusBadge(transaction.status, transaction).label}\n          </span>\n        </div>\n      </div>\n\n      {/* Overview */}\n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\n        <div className=\"lg:col-span-2\">\n          <InfoCard title=\"Message Information\" icon={Mail}>\n            <dl className=\"divide-y divide-gray-200\">\n              <InfoRow \n                label=\"From\" \n                value={formatEmail(transaction.mail_from?.address)} \n              />\n              <InfoRow \n                label=\"To\" \n                value={\n                  transaction.rcpt_to?.length > 0 \n                    ? transaction.rcpt_to.map(r => formatEmail(r.address)).join(', ')\n                    : 'No recipients'\n                } \n              />\n              <InfoRow \n                label=\"Subject\" \n                value={formatSubject(transaction.message?.subject)} \n              />\n              <InfoRow \n                label=\"Message ID\" \n                value={transaction.message?.message_id || 'N/A'} \n              />\n              <InfoRow \n                label=\"Size\" \n                value={formatFileSize(transaction.message?.size)} \n              />\n              <InfoRow \n                label=\"HELO/EHLO\" \n                value={transaction.helo || 'N/A'} \n              />\n            </dl>\n          </InfoCard>\n        </div>\n\n        <div>\n          <InfoCard title=\"Timing Information\" icon={Clock}>\n            <dl className=\"divide-y divide-gray-200\">\n              <InfoRow \n                label=\"Received\" \n                value={formatDate(transaction.timestamp)} \n              />\n              <InfoRow \n                label=\"Relative\" \n                value={formatRelativeTime(transaction.timestamp)} \n              />\n              <InfoRow \n                label=\"Processing Time\" \n                value={formatProcessingTime(transaction.processing_time)} \n              />\n              {transaction.timestamps?.connect && (\n                <InfoRow \n                  label=\"Connected\" \n                  value={formatDate(transaction.timestamps.connect, 'HH:mm:ss.SSS')} \n                />\n              )}\n              {transaction.timestamps?.disconnect && (\n                <InfoRow \n                  label=\"Disconnected\" \n                  value={formatDate(transaction.timestamps.disconnect, 'HH:mm:ss.SSS')} \n                />\n              )}\n            </dl>\n          </InfoCard>\n        </div>\n      </div>\n\n      {/* Connection & Authentication */}\n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n        <InfoCard title=\"Connection Information\" icon={Server}>\n          <dl className=\"divide-y divide-gray-200\">\n            <InfoRow \n              label=\"Remote IP\" \n              value={transaction.connection?.remote_ip || 'N/A'} \n            />\n            <InfoRow \n              label=\"Remote Host\" \n              value={transaction.connection?.remote_host || 'N/A'} \n            />\n            <InfoRow \n              label=\"Local IP\" \n              value={transaction.connection?.local_ip || 'N/A'} \n            />\n            <InfoRow \n              label=\"Local Port\" \n              value={transaction.connection?.local_port || 'N/A'} \n            />\n            <InfoRow \n              label=\"TLS Enabled\" \n              value={transaction.connection?.tls?.enabled ? 'Yes' : 'No'} \n            />\n            {transaction.connection?.tls?.cipher && (\n              <InfoRow \n                label=\"TLS Cipher\" \n                value={transaction.connection.tls.cipher} \n              />\n            )}\n          </dl>\n        </InfoCard>\n\n        <InfoCard title=\"Authentication\" icon={Shield}>\n          <dl className=\"divide-y divide-gray-200\">\n            <InfoRow \n              label=\"SPF\" \n              value={transaction.authentication?.spf || 'N/A'} \n            />\n            <InfoRow \n              label=\"DKIM\" \n              value={transaction.authentication?.dkim || 'N/A'} \n            />\n            <InfoRow \n              label=\"DMARC\" \n              value={transaction.authentication?.dmarc || 'N/A'} \n            />\n          </dl>\n        </InfoCard>\n      </div>\n\n      {/* Status-specific Information */}\n      {consolidatedStatus === 'delivered' && <DeliveredInfo />}\n      {consolidatedStatus === 'blocked' && <BlockedInfo />}\n      {consolidatedStatus === 'failed' && <FailedInfo />}\n      {consolidatedStatus === 'disconnected' && <DisconnectedInfo />}\n\n      {/* Headers */}\n      {transaction.headers && Object.keys(transaction.headers).length > 0 && (\n        <InfoCard title=\"Email Headers\" icon={FileText}>\n          <div className=\"space-y-3\">\n            {Object.entries(transaction.headers).map(([key, value]) => (\n              <div key={key} className=\"border-b border-gray-200 pb-3 last:border-b-0\">\n                <dt className=\"text-sm font-medium text-gray-500 mb-1 capitalize\">\n                  {key.replace(/-/g, ' ')}\n                </dt>\n                <dd className=\"text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded break-all\">\n                  {formatHeaderValue(value)}\n                </dd>\n              </div>\n            ))}\n          </div>\n        </InfoCard>\n      )}\n\n      {/* Timeline */}\n      {transaction.timestamps && (\n        <InfoCard title=\"Transaction Timeline\" icon={Calendar}>\n          <div className=\"flow-root\">\n            <ul className=\"-mb-8\">\n              {Object.entries(transaction.timestamps)\n                .sort(([,a], [,b]) => new Date(a) - new Date(b))\n                .map(([event, timestamp], index, array) => (\n                <li key={event}>\n                  <div className=\"relative pb-8\">\n                    {index !== array.length - 1 && (\n                      <span className=\"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200\" />\n                    )}\n                    <div className=\"relative flex space-x-3\">\n                      <div>\n                        <span className=\"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white\">\n                          <Hash className=\"h-4 w-4 text-white\" />\n                        </span>\n                      </div>\n                      <div className=\"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\">\n                        <div>\n                          <p className=\"text-sm text-gray-500 capitalize\">\n                            {event.replace(/_/g, ' ')}\n                          </p>\n                        </div>\n                        <div className=\"text-right text-sm whitespace-nowrap text-gray-500\">\n                          <time dateTime={timestamp}>\n                            {formatDate(timestamp, 'HH:mm:ss.SSS')}\n                          </time>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </InfoCard>\n      )}\n\n      {/* Errors */}\n      {transaction.errors && transaction.errors.length > 0 && (\n        <InfoCard title=\"Errors\" icon={AlertCircle}>\n          <div className=\"space-y-2\">\n            {transaction.errors.map((error, index) => (\n              <div key={index} className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                <p className=\"text-sm text-red-800\">{error}</p>\n              </div>\n            ))}\n          </div>\n        </InfoCard>\n      )}\n    </div>\n  );\n};\n\nexport default TransactionDetail;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,IAAI,KAAQ,kBAAkB,CAClD,OACEC,SAAS,CACTC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,QAAQ,CACRC,IAAI,CACJC,KAAK,CACLC,QAAQ,CACRC,IAAI,CACJC,WAAW,KACN,cAAc,CACrB,OAASC,cAAc,KAAQ,iBAAiB,CAChD,OACEC,UAAU,CACVC,kBAAkB,CAClBC,cAAc,CACdC,4BAA4B,CAC5BC,WAAW,CACXC,aAAa,CACbC,cAAc,CACdC,oBAAoB,CACpBC,iBAAiB,KACZ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7B,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,oBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAC9B,KAAM,CAAEC,EAAG,CAAC,CAAGhD,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAEiD,IAAI,CAAEC,WAAW,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGvC,cAAc,CAACmC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAAK,kBAAkB,CAAGH,WAAW,CAAGjC,4BAA4B,CAACiC,WAAW,CAAC,CAAG,IAAI,CAEzF,GAAIC,OAAO,CAAE,CACX,mBACEzB,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/B,IAAA,QAAK8B,SAAS,CAAC,yBAAyB,CAAM,CAAC,cAC/C9B,IAAA,MAAG8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,gCAA8B,CAAG,CAAC,EACzE,CAAC,CAEV,CAEA,GAAIH,KAAK,CAAE,CACT,mBACE1B,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/B,IAAA,CAACZ,WAAW,EAAC0C,SAAS,CAAC,gCAAgC,CAAE,CAAC,cAC1D9B,IAAA,OAAI8B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cACrF/B,IAAA,MAAG8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEH,KAAK,CAAI,CAAC,cACrD5B,IAAA,CAACvB,IAAI,EAACuD,EAAE,CAAC,eAAe,CAACF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,sBAEtD,CAAM,CAAC,EACJ,CAAC,CAEV,CAEA,GAAI,CAACL,WAAW,CAAE,CAChB,mBACExB,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/B,IAAA,CAACrB,IAAI,EAACmD,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACpD9B,IAAA,OAAI8B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACjF/B,IAAA,MAAG8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,+CAA6C,CAAG,CAAC,cAC3F/B,IAAA,CAACvB,IAAI,EAACuD,EAAE,CAAC,eAAe,CAACF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,sBAEtD,CAAM,CAAC,EACJ,CAAC,CAEV,CAEA,KAAM,CAAAE,QAAQ,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,IAAI,CAAEC,IAAI,CAAEN,QAAS,CAAC,CAAAG,IAAA,oBAC/ChC,KAAA,QAAK4B,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvB7B,KAAA,QAAK4B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC/B,IAAA,CAACqC,IAAI,EAACP,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC/C9B,IAAA,OAAI8B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEI,KAAK,CAAK,CAAC,EAC3D,CAAC,CACLJ,QAAQ,EACN,CAAC,EACP,CAED,KAAM,CAAAO,OAAO,CAAGC,KAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEX,SAAS,CAAG,EAAG,CAAC,CAAAS,KAAA,oBAC/CrC,KAAA,QAAK4B,SAAS,CAAE,6BAA6BA,SAAS,EAAG,CAAAC,QAAA,eACvD/B,IAAA,OAAI8B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAES,KAAK,CAAK,CAAC,cAC9DxC,IAAA,OAAI8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEU,KAAK,CAAK,CAAC,EAC1D,CAAC,EACP,CAED;AACA,KAAM,CAAAC,aAAa,CAAGA,CAAA,QAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,oBACpB9C,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,sBAAsB,CAACC,IAAI,CAAEzD,IAAK,CAAAoD,QAAA,cAChD7B,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAEf,WAAW,CAACqB,MAAM,GAAK,SAAS,CAAG,sBAAsB,CAAG,wBAAyB,CAC7F,CAAC,CACDrB,WAAW,CAACsB,cAAc,eACzB9C,KAAA,CAAAE,SAAA,EAAA2B,QAAA,eACE/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAE,EAAAE,qBAAA,CAAAjB,WAAW,CAACsB,cAAc,CAACC,GAAG,UAAAN,qBAAA,iBAA9BA,qBAAA,CAAgCO,MAAM,GAAI,KAAM,CACxD,CAAC,cACFlD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAE,EAAAG,sBAAA,CAAAlB,WAAW,CAACsB,cAAc,CAACG,IAAI,UAAAP,sBAAA,iBAA/BA,sBAAA,CAAiCM,MAAM,GAAI,KAAM,CACzD,CAAC,cACFlD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAE,EAAAI,sBAAA,CAAAnB,WAAW,CAACsB,cAAc,CAACI,KAAK,UAAAP,sBAAA,iBAAhCA,sBAAA,CAAkCK,MAAM,GAAI,KAAM,CAC1D,CAAC,EACF,CACH,cACDlD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE5C,oBAAoB,CAAC6B,WAAW,CAAC2B,eAAe,CAAE,CAC1D,CAAC,CACD,EAAAP,qBAAA,CAAApB,WAAW,CAAC4B,UAAU,UAAAR,qBAAA,iBAAtBA,qBAAA,CAAwBS,KAAK,gBAC5BvD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAEnD,UAAU,CAACoC,WAAW,CAAC4B,UAAU,CAACC,KAAK,CAAE,CACjD,CACF,EACC,CAAC,CACG,CAAC,EACZ,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,QAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAClB7D,KAAA,CAAC+B,QAAQ,EAACE,KAAK,CAAC,mBAAmB,CAACC,IAAI,CAAEtD,MAAO,CAAAiD,QAAA,eAC/C/B,IAAA,QAAK8B,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClE7B,KAAA,QAAK4B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB/B,IAAA,CAACZ,WAAW,EAAC0C,SAAS,CAAC,kCAAkC,CAAE,CAAC,cAC5D5B,KAAA,QAAA6B,QAAA,eACE/B,IAAA,OAAI8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACnE/B,IAAA,MAAG8B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACrCL,WAAW,CAACsC,gBAAgB,EAAI,kCAAkC,CAClE,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACN9D,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,EACrCL,WAAW,CAACsB,cAAc,eACzB9C,KAAA,CAAAE,SAAA,EAAA2B,QAAA,eACE/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,cACHzC,IAAA,SAAM8B,SAAS,CAAE,EAAA2B,sBAAA,CAAA/B,WAAW,CAACsB,cAAc,CAACC,GAAG,UAAAQ,sBAAA,iBAA9BA,sBAAA,CAAgCP,MAAM,IAAK,MAAM,CAAG,0BAA0B,CAAG,EAAG,CAAAnB,QAAA,CAClG,EAAA2B,sBAAA,CAAAhC,WAAW,CAACsB,cAAc,CAACC,GAAG,UAAAS,sBAAA,iBAA9BA,sBAAA,CAAgCR,MAAM,GAAI,KAAK,CAC5C,CACP,CACF,CAAC,cACFlD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,cACHzC,IAAA,SAAM8B,SAAS,CAAE,EAAA6B,sBAAA,CAAAjC,WAAW,CAACsB,cAAc,CAACG,IAAI,UAAAQ,sBAAA,iBAA/BA,sBAAA,CAAiCT,MAAM,IAAK,MAAM,CAAG,0BAA0B,CAAG,EAAG,CAAAnB,QAAA,CACnG,EAAA6B,sBAAA,CAAAlC,WAAW,CAACsB,cAAc,CAACG,IAAI,UAAAS,sBAAA,iBAA/BA,sBAAA,CAAiCV,MAAM,GAAI,KAAK,CAC7C,CACP,CACF,CAAC,cACFlD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,cACHzC,IAAA,SAAM8B,SAAS,CAAE,EAAA+B,sBAAA,CAAAnC,WAAW,CAACsB,cAAc,CAACI,KAAK,UAAAS,sBAAA,iBAAhCA,sBAAA,CAAkCX,MAAM,IAAK,MAAM,CAAG,0BAA0B,CAAG,EAAG,CAAAnB,QAAA,CACpG,EAAA+B,sBAAA,CAAApC,WAAW,CAACsB,cAAc,CAACI,KAAK,UAAAU,sBAAA,iBAAhCA,sBAAA,CAAkCZ,MAAM,GAAI,KAAK,CAC9C,CACP,CACF,CAAC,EACF,CACH,cACDlD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,gBAAgB,CACtBC,KAAK,CAAEnD,UAAU,CAAC,EAAAyE,sBAAA,CAAArC,WAAW,CAAC4B,UAAU,UAAAS,sBAAA,iBAAtBA,sBAAA,CAAwBE,UAAU,GAAIvC,WAAW,CAACwC,SAAS,CAAE,CAChF,CAAC,EACA,CAAC,EACG,CAAC,EACZ,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,QAAAC,mBAAA,CAAAC,oBAAA,CAAAC,sBAAA,oBACjBpE,KAAA,CAAC+B,QAAQ,EAACE,KAAK,CAAC,iBAAiB,CAACC,IAAI,CAAEhD,WAAY,CAAA2C,QAAA,eAClD/B,IAAA,QAAK8B,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClE7B,KAAA,QAAK4B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB/B,IAAA,CAACZ,WAAW,EAAC0C,SAAS,CAAC,kCAAkC,CAAE,CAAC,cAC5D5B,KAAA,QAAA6B,QAAA,eACE/B,IAAA,OAAI8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACvE/B,IAAA,MAAG8B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACrC,EAAAqC,mBAAA,CAAA1C,WAAW,CAAC6C,MAAM,UAAAH,mBAAA,iBAAlBA,mBAAA,CAAoBI,MAAM,EAAG,CAAC,CAC3B9C,WAAW,CAAC6C,MAAM,CAAC,CAAC,CAAC,CACrB,iDAAiD,CACpD,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACNrE,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,EACrC,EAAAsC,oBAAA,CAAA3C,WAAW,CAAC6C,MAAM,UAAAF,oBAAA,iBAAlBA,oBAAA,CAAoBG,MAAM,EAAG,CAAC,eAC7BxE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAEf,WAAW,CAAC6C,MAAM,CAACC,MAAO,CAClC,CACF,cACDxE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAEnD,UAAU,CAAC,EAAAgF,sBAAA,CAAA5C,WAAW,CAAC4B,UAAU,UAAAgB,sBAAA,iBAAtBA,sBAAA,CAAwBL,UAAU,GAAIvC,WAAW,CAACwC,SAAS,CAAE,CAChF,CAAC,cACFlE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE5C,oBAAoB,CAAC6B,WAAW,CAAC2B,eAAe,CAAE,CAC1D,CAAC,EACA,CAAC,EACG,CAAC,EACZ,CAED,KAAM,CAAAoB,gBAAgB,CAAGA,CAAA,GAAM,KAAAC,sBAAA,CAAAC,sBAAA,CAC7B,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAtB,UAAU,CAAG5B,WAAW,CAAC4B,UAAU,EAAI,CAAC,CAAC,CAC/C,GAAI,CAACA,UAAU,CAACuB,IAAI,CAAE,MAAO,2BAA2B,CACxD,GAAI,CAACvB,UAAU,CAACwB,SAAS,CAAE,MAAO,8BAA8B,CAChE,GAAI,CAACxB,UAAU,CAACyB,UAAU,CAAE,MAAO,8BAA8B,CACjE,GAAI,CAACzB,UAAU,CAAC0B,aAAa,CAAE,MAAO,0BAA0B,CAChE,MAAO,+BAA+B,CACxC,CAAC,CAED,mBACE9E,KAAA,CAAC+B,QAAQ,EAACE,KAAK,CAAC,oBAAoB,CAACC,IAAI,CAAEvD,MAAO,CAAAkD,QAAA,eAChD/B,IAAA,QAAK8B,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxE7B,KAAA,QAAK4B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB/B,IAAA,CAACZ,WAAW,EAAC0C,SAAS,CAAC,qCAAqC,CAAE,CAAC,cAC/D5B,KAAA,QAAA6B,QAAA,eACE/B,IAAA,OAAI8B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACxE7B,KAAA,MAAG4B,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,sBACtB,CAAC6C,qBAAqB,CAAC,CAAC,EAC3C,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACN1E,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,qBAAqB,CAC3BC,KAAK,CAAEmC,qBAAqB,CAAC,CAAE,CAChC,CAAC,cACF5E,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,qBAAqB,CAC3BC,KAAK,CAAE5C,oBAAoB,CAAC6B,WAAW,CAAC2B,eAAe,CAAE,CAC1D,CAAC,cACFrD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAEnD,UAAU,CAAC,EAAAoF,sBAAA,CAAAhD,WAAW,CAAC4B,UAAU,UAAAoB,sBAAA,iBAAtBA,sBAAA,CAAwBT,UAAU,GAAIvC,WAAW,CAACwC,SAAS,CAAE,CAChF,CAAC,CACD,EAAAS,sBAAA,CAAAjD,WAAW,CAAC4B,UAAU,UAAAqB,sBAAA,iBAAtBA,sBAAA,CAAwBM,OAAO,gBAC9BjF,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAEnD,UAAU,CAACoC,WAAW,CAAC4B,UAAU,CAAC2B,OAAO,CAAE,CACnD,CACF,EACC,CAAC,EACG,CAAC,CAEf,CAAC,CAED,mBACE/E,KAAA,QAAK4B,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB/B,IAAA,QAAK8B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C7B,KAAA,CAACzB,IAAI,EACHuD,EAAE,CAAC,eAAe,CAClBF,SAAS,CAAC,yEAAyE,CAAAC,QAAA,eAEnF/B,IAAA,CAACtB,SAAS,EAACoD,SAAS,CAAC,cAAc,CAAE,CAAC,uBAExC,EAAM,CAAC,CACJ,CAAC,cAEN5B,KAAA,QAAK4B,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD7B,KAAA,QAAK4B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/B,IAAA,OAAI8B,SAAS,CAAC,oEAAoE,CAAAC,QAAA,CAAC,qBAEnF,CAAI,CAAC,cACL7B,KAAA,MAAG4B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,kBACxB,CAACL,WAAW,CAACwD,cAAc,EAC1C,CAAC,EACD,CAAC,cACNlF,IAAA,QAAK8B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxC/B,IAAA,SAAM8B,SAAS,CAAE,SAAStC,cAAc,CAACkC,WAAW,CAACqB,MAAM,CAAErB,WAAW,CAAC,CAACI,SAAS,sBAAuB,CAAAC,QAAA,CACvGvC,cAAc,CAACkC,WAAW,CAACqB,MAAM,CAAErB,WAAW,CAAC,CAACc,KAAK,CAClD,CAAC,CACJ,CAAC,EACH,CAAC,cAGNtC,KAAA,QAAK4B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD/B,IAAA,QAAK8B,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B/B,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,qBAAqB,CAACC,IAAI,CAAEzD,IAAK,CAAAoD,QAAA,cAC/C7B,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,MAAM,CACZC,KAAK,CAAE/C,WAAW,EAAAY,qBAAA,CAACoB,WAAW,CAACoD,SAAS,UAAAxE,qBAAA,iBAArBA,qBAAA,CAAuB6E,OAAO,CAAE,CACpD,CAAC,cACFnF,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,IAAI,CACVC,KAAK,CACH,EAAAlC,oBAAA,CAAAmB,WAAW,CAAC0D,OAAO,UAAA7E,oBAAA,iBAAnBA,oBAAA,CAAqBiE,MAAM,EAAG,CAAC,CAC3B9C,WAAW,CAAC0D,OAAO,CAACC,GAAG,CAACC,CAAC,EAAI5F,WAAW,CAAC4F,CAAC,CAACH,OAAO,CAAC,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAC/D,eACL,CACF,CAAC,cACFvF,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,SAAS,CACfC,KAAK,CAAE9C,aAAa,EAAAa,oBAAA,CAACkB,WAAW,CAAC8D,OAAO,UAAAhF,oBAAA,iBAAnBA,oBAAA,CAAqBiF,OAAO,CAAE,CACpD,CAAC,cACFzF,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAE,EAAAhC,qBAAA,CAAAiB,WAAW,CAAC8D,OAAO,UAAA/E,qBAAA,iBAAnBA,qBAAA,CAAqBiF,UAAU,GAAI,KAAM,CACjD,CAAC,cACF1F,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,MAAM,CACZC,KAAK,CAAE7C,cAAc,EAAAc,qBAAA,CAACgB,WAAW,CAAC8D,OAAO,UAAA9E,qBAAA,iBAAnBA,qBAAA,CAAqBiF,IAAI,CAAE,CAClD,CAAC,cACF3F,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAEf,WAAW,CAACmD,IAAI,EAAI,KAAM,CAClC,CAAC,EACA,CAAC,CACG,CAAC,CACR,CAAC,cAEN7E,IAAA,QAAA+B,QAAA,cACE/B,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,oBAAoB,CAACC,IAAI,CAAExD,KAAM,CAAAmD,QAAA,cAC/C7B,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAEnD,UAAU,CAACoC,WAAW,CAACwC,SAAS,CAAE,CAC1C,CAAC,cACFlE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAElD,kBAAkB,CAACmC,WAAW,CAACwC,SAAS,CAAE,CAClD,CAAC,cACFlE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE5C,oBAAoB,CAAC6B,WAAW,CAAC2B,eAAe,CAAE,CAC1D,CAAC,CACD,EAAA1C,sBAAA,CAAAe,WAAW,CAAC4B,UAAU,UAAA3C,sBAAA,iBAAtBA,sBAAA,CAAwBsE,OAAO,gBAC9BjF,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAEnD,UAAU,CAACoC,WAAW,CAAC4B,UAAU,CAAC2B,OAAO,CAAE,cAAc,CAAE,CACnE,CACF,CACA,EAAArE,sBAAA,CAAAc,WAAW,CAAC4B,UAAU,UAAA1C,sBAAA,iBAAtBA,sBAAA,CAAwBqD,UAAU,gBACjCjE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAEnD,UAAU,CAACoC,WAAW,CAAC4B,UAAU,CAACW,UAAU,CAAE,cAAc,CAAE,CACtE,CACF,EACC,CAAC,CACG,CAAC,CACR,CAAC,EACH,CAAC,cAGN/D,KAAA,QAAK4B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD/B,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,wBAAwB,CAACC,IAAI,CAAEvD,MAAO,CAAAkD,QAAA,cACpD7B,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAE,EAAA5B,qBAAA,CAAAa,WAAW,CAACkE,UAAU,UAAA/E,qBAAA,iBAAtBA,qBAAA,CAAwBgF,SAAS,GAAI,KAAM,CACnD,CAAC,cACF7F,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAE,EAAA3B,sBAAA,CAAAY,WAAW,CAACkE,UAAU,UAAA9E,sBAAA,iBAAtBA,sBAAA,CAAwBgF,WAAW,GAAI,KAAM,CACrD,CAAC,cACF9F,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAE,EAAA1B,sBAAA,CAAAW,WAAW,CAACkE,UAAU,UAAA7E,sBAAA,iBAAtBA,sBAAA,CAAwBgF,QAAQ,GAAI,KAAM,CAClD,CAAC,cACF/F,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAE,EAAAzB,sBAAA,CAAAU,WAAW,CAACkE,UAAU,UAAA5E,sBAAA,iBAAtBA,sBAAA,CAAwBgF,UAAU,GAAI,KAAM,CACpD,CAAC,cACFhG,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAE,CAAAxB,sBAAA,CAAAS,WAAW,CAACkE,UAAU,UAAA3E,sBAAA,YAAAC,sBAAA,CAAtBD,sBAAA,CAAwBgF,GAAG,UAAA/E,sBAAA,WAA3BA,sBAAA,CAA6BgF,OAAO,CAAG,KAAK,CAAG,IAAK,CAC5D,CAAC,CACD,EAAA/E,sBAAA,CAAAO,WAAW,CAACkE,UAAU,UAAAzE,sBAAA,kBAAAC,sBAAA,CAAtBD,sBAAA,CAAwB8E,GAAG,UAAA7E,sBAAA,iBAA3BA,sBAAA,CAA6B+E,MAAM,gBAClCnG,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAEf,WAAW,CAACkE,UAAU,CAACK,GAAG,CAACE,MAAO,CAC1C,CACF,EACC,CAAC,CACG,CAAC,cAEXnG,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,gBAAgB,CAACC,IAAI,CAAEtD,MAAO,CAAAiD,QAAA,cAC5C7B,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,KAAK,CACXC,KAAK,CAAE,EAAApB,sBAAA,CAAAK,WAAW,CAACsB,cAAc,UAAA3B,sBAAA,iBAA1BA,sBAAA,CAA4B4B,GAAG,GAAI,KAAM,CACjD,CAAC,cACFjD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,MAAM,CACZC,KAAK,CAAE,EAAAnB,sBAAA,CAAAI,WAAW,CAACsB,cAAc,UAAA1B,sBAAA,iBAA1BA,sBAAA,CAA4B6B,IAAI,GAAI,KAAM,CAClD,CAAC,cACFnD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,OAAO,CACbC,KAAK,CAAE,EAAAlB,uBAAA,CAAAG,WAAW,CAACsB,cAAc,UAAAzB,uBAAA,iBAA1BA,uBAAA,CAA4B6B,KAAK,GAAI,KAAM,CACnD,CAAC,EACA,CAAC,CACG,CAAC,EACR,CAAC,CAGLvB,kBAAkB,GAAK,WAAW,eAAI7B,IAAA,CAAC0C,aAAa,GAAE,CAAC,CACvDb,kBAAkB,GAAK,SAAS,eAAI7B,IAAA,CAACwD,WAAW,GAAE,CAAC,CACnD3B,kBAAkB,GAAK,QAAQ,eAAI7B,IAAA,CAACmE,UAAU,GAAE,CAAC,CACjDtC,kBAAkB,GAAK,cAAc,eAAI7B,IAAA,CAACyE,gBAAgB,GAAE,CAAC,CAG7D/C,WAAW,CAAC0E,OAAO,EAAIC,MAAM,CAACC,IAAI,CAAC5E,WAAW,CAAC0E,OAAO,CAAC,CAAC5B,MAAM,CAAG,CAAC,eACjExE,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,eAAe,CAACC,IAAI,CAAErD,QAAS,CAAAgD,QAAA,cAC7C/B,IAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBsE,MAAM,CAACE,OAAO,CAAC7E,WAAW,CAAC0E,OAAO,CAAC,CAACf,GAAG,CAACmB,KAAA,MAAC,CAACC,GAAG,CAAEhE,KAAK,CAAC,CAAA+D,KAAA,oBACpDtG,KAAA,QAAe4B,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eACtE/B,IAAA,OAAI8B,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D0E,GAAG,CAACC,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CACrB,CAAC,cACL1G,IAAA,OAAI8B,SAAS,CAAC,kEAAkE,CAAAC,QAAA,CAC7EjC,iBAAiB,CAAC2C,KAAK,CAAC,CACvB,CAAC,GANGgE,GAOL,CAAC,EACP,CAAC,CACC,CAAC,CACE,CACX,CAGA/E,WAAW,CAAC4B,UAAU,eACrBtD,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,sBAAsB,CAACC,IAAI,CAAElD,QAAS,CAAA6C,QAAA,cACpD/B,IAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB/B,IAAA,OAAI8B,SAAS,CAAC,OAAO,CAAAC,QAAA,CAClBsE,MAAM,CAACE,OAAO,CAAC7E,WAAW,CAAC4B,UAAU,CAAC,CACpCqD,IAAI,CAAC,CAAAC,KAAA,CAAAC,KAAA,OAAC,EAAEC,CAAC,CAAC,CAAAF,KAAA,IAAE,EAAEG,CAAC,CAAC,CAAAF,KAAA,OAAK,IAAI,CAAAG,IAAI,CAACF,CAAC,CAAC,CAAG,GAAI,CAAAE,IAAI,CAACD,CAAC,CAAC,GAAC,CAC/C1B,GAAG,CAAC,CAAA4B,KAAA,CAAqBC,KAAK,CAAEC,KAAK,OAAhC,CAACC,KAAK,CAAElD,SAAS,CAAC,CAAA+C,KAAA,oBACxBjH,IAAA,OAAA+B,QAAA,cACE7B,KAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3BmF,KAAK,GAAKC,KAAK,CAAC3C,MAAM,CAAG,CAAC,eACzBxE,IAAA,SAAM8B,SAAS,CAAC,uDAAuD,CAAE,CAC1E,cACD5B,KAAA,QAAK4B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC/B,IAAA,QAAA+B,QAAA,cACE/B,IAAA,SAAM8B,SAAS,CAAC,wFAAwF,CAAAC,QAAA,cACtG/B,IAAA,CAACb,IAAI,EAAC2C,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACnC,CAAC,CACJ,CAAC,cACN5B,KAAA,QAAK4B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnE/B,IAAA,QAAA+B,QAAA,cACE/B,IAAA,MAAG8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC5CqF,KAAK,CAACV,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CACxB,CAAC,CACD,CAAC,cACN1G,IAAA,QAAK8B,SAAS,CAAC,oDAAoD,CAAAC,QAAA,cACjE/B,IAAA,SAAMqH,QAAQ,CAAEnD,SAAU,CAAAnC,QAAA,CACvBzC,UAAU,CAAC4E,SAAS,CAAE,cAAc,CAAC,CAClC,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EAxBCkD,KAyBL,CAAC,EACN,CAAC,CACA,CAAC,CACF,CAAC,CACE,CACX,CAGA1F,WAAW,CAAC6C,MAAM,EAAI7C,WAAW,CAAC6C,MAAM,CAACC,MAAM,CAAG,CAAC,eAClDxE,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,QAAQ,CAACC,IAAI,CAAEhD,WAAY,CAAA2C,QAAA,cACzC/B,IAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBL,WAAW,CAAC6C,MAAM,CAACc,GAAG,CAAC,CAACzD,KAAK,CAAEsF,KAAK,gBACnClH,IAAA,QAAiB8B,SAAS,CAAC,gDAAgD,CAAAC,QAAA,cACzE/B,IAAA,MAAG8B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAEH,KAAK,CAAI,CAAC,EADvCsF,KAEL,CACN,CAAC,CACC,CAAC,CACE,CACX,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7G,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}