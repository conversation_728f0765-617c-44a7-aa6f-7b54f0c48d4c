{"ast": null, "code": "import React from'react';import{useParams,Link}from'react-router-dom';import{ArrowLeft,Mail,Clock,Server,Shield,FileText,User,Globe,Calendar,Hash,AlertCircle}from'lucide-react';import{useTransaction}from'../hooks/useApi';import{formatDate,formatRelativeTime,getStatusBadge,consolidateTransactionStatus,formatEmail,formatSubject,formatFileSize,formatProcessingTime,formatHeaderValue,getTransactionRecipients}from'../utils/formatters';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const TransactionDetail=()=>{var _transaction$mail_fro,_transaction$sender,_transaction$message,_transaction$message2,_transaction$message3,_transaction$timestam6,_transaction$timestam7,_transaction$connecti5,_transaction$connecti6,_transaction$connecti7,_transaction$connecti8,_transaction$connecti9,_transaction$connecti0,_transaction$connecti1,_transaction$connecti10,_transaction$authenti0,_transaction$authenti1,_transaction$authenti10;const{id}=useParams();const{data:transaction,loading,error}=useTransaction(id);// Get consolidated status for conditional rendering\nconst consolidatedStatus=transaction?consolidateTransactionStatus(transaction):null;if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner mx-auto\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-4 text-sm text-gray-500\",children:\"Loading transaction details...\"})]});}if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"mx-auto h-12 w-12 text-red-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"Error loading transaction\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:error}),/*#__PURE__*/_jsx(Link,{to:\"/transactions\",className:\"mt-4 btn-primary\",children:\"Back to Transactions\"})]});}if(!transaction){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(Mail,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"Transaction not found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"The requested transaction could not be found.\"}),/*#__PURE__*/_jsx(Link,{to:\"/transactions\",className:\"mt-4 btn-primary\",children:\"Back to Transactions\"})]});}const InfoCard=_ref=>{let{title,icon:Icon,children}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"card p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(Icon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:title})]}),children]});};const InfoRow=_ref2=>{let{label,value,className=''}=_ref2;return/*#__PURE__*/_jsxs(\"div\",{className:`flex justify-between py-2 ${className}`,children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500\",children:label}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-sm text-gray-900 text-right\",children:value})]});};// Status-specific information components\nconst DeliveredInfo=()=>{var _transaction$authenti,_transaction$authenti2,_transaction$authenti3,_transaction$timestam;return/*#__PURE__*/_jsx(InfoCard,{title:\"Delivery Information\",icon:Mail,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Delivery Status\",value:transaction.status==='relayed'?'Successfully Relayed':'Successfully Delivered'}),transaction.delivery_details&&Array.isArray(transaction.delivery_details)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Destination Server\",value:transaction.delivery_details[0]||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Server IP\",value:transaction.delivery_details[1]||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"SMTP Response\",value:transaction.delivery_details[2]||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Delivery Time\",value:transaction.delivery_details[3]?`${transaction.delivery_details[3]}s`:'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Port\",value:transaction.delivery_details[4]||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Protocol\",value:transaction.delivery_details[5]||'N/A'})]}),transaction.authentication&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(InfoRow,{label:\"SPF Result\",value:((_transaction$authenti=transaction.authentication.spf)===null||_transaction$authenti===void 0?void 0:_transaction$authenti.result)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DKIM Result\",value:((_transaction$authenti2=transaction.authentication.dkim)===null||_transaction$authenti2===void 0?void 0:_transaction$authenti2.result)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DMARC Result\",value:((_transaction$authenti3=transaction.authentication.dmarc)===null||_transaction$authenti3===void 0?void 0:_transaction$authenti3.result)||'N/A'})]}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)}),((_transaction$timestam=transaction.timestamps)===null||_transaction$timestam===void 0?void 0:_transaction$timestam.queue)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Queued At\",value:formatDate(transaction.timestamps.queue)})]})});};const BlockedInfo=()=>{var _transaction$authenti4,_transaction$authenti5,_transaction$authenti6,_transaction$authenti7,_transaction$authenti8,_transaction$authenti9,_transaction$timestam2;return/*#__PURE__*/_jsxs(InfoCard,{title:\"Rejection Details\",icon:Shield,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"h-5 w-5 text-red-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-red-800\",children:\"Email Blocked\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-700 mt-1\",children:transaction.rejection_reason||'Email was rejected by the server'})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[transaction.authentication&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(InfoRow,{label:\"SPF Check\",value:/*#__PURE__*/_jsx(\"span\",{className:((_transaction$authenti4=transaction.authentication.spf)===null||_transaction$authenti4===void 0?void 0:_transaction$authenti4.result)==='fail'?'text-red-600 font-medium':'',children:((_transaction$authenti5=transaction.authentication.spf)===null||_transaction$authenti5===void 0?void 0:_transaction$authenti5.result)||'N/A'})}),/*#__PURE__*/_jsx(InfoRow,{label:\"DKIM Check\",value:/*#__PURE__*/_jsx(\"span\",{className:((_transaction$authenti6=transaction.authentication.dkim)===null||_transaction$authenti6===void 0?void 0:_transaction$authenti6.result)==='fail'?'text-red-600 font-medium':'',children:((_transaction$authenti7=transaction.authentication.dkim)===null||_transaction$authenti7===void 0?void 0:_transaction$authenti7.result)||'N/A'})}),/*#__PURE__*/_jsx(InfoRow,{label:\"DMARC Check\",value:/*#__PURE__*/_jsx(\"span\",{className:((_transaction$authenti8=transaction.authentication.dmarc)===null||_transaction$authenti8===void 0?void 0:_transaction$authenti8.result)==='fail'?'text-red-600 font-medium':'',children:((_transaction$authenti9=transaction.authentication.dmarc)===null||_transaction$authenti9===void 0?void 0:_transaction$authenti9.result)||'N/A'})})]}),/*#__PURE__*/_jsx(InfoRow,{label:\"Rejection Time\",value:formatDate(((_transaction$timestam2=transaction.timestamps)===null||_transaction$timestam2===void 0?void 0:_transaction$timestam2.disconnect)||transaction.timestamp)})]})]});};const FailedInfo=()=>{var _transaction$errors,_transaction$errors2,_transaction$timestam3;return/*#__PURE__*/_jsxs(InfoCard,{title:\"Failure Details\",icon:AlertCircle,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"h-5 w-5 text-red-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-red-800\",children:\"Technical Failure\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-700 mt-1\",children:((_transaction$errors=transaction.errors)===null||_transaction$errors===void 0?void 0:_transaction$errors.length)>0?transaction.errors[0]:'Email processing failed due to technical issues'})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[((_transaction$errors2=transaction.errors)===null||_transaction$errors2===void 0?void 0:_transaction$errors2.length)>0&&/*#__PURE__*/_jsx(InfoRow,{label:\"Error Count\",value:transaction.errors.length}),/*#__PURE__*/_jsx(InfoRow,{label:\"Failure Time\",value:formatDate(((_transaction$timestam3=transaction.timestamps)===null||_transaction$timestam3===void 0?void 0:_transaction$timestam3.disconnect)||transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)})]})]});};const DisconnectedInfo=()=>{var _transaction$timestam4,_transaction$timestam5,_transaction$connecti3,_transaction$connecti4;const getDisconnectionStage=()=>{const timestamps=transaction.timestamps||{};// Based on your real data: client connected, sent HELO, but disconnected before MAIL FROM\nif(!timestamps.helo)return'During initial connection (before HELO)';if(!timestamps.mail_from)return'After HELO, before MAIL FROM command';if(!timestamps.data_start)return'After MAIL FROM, before DATA command';if(!timestamps.data_complete)return'During DATA transmission';if(!timestamps.queue)return'After DATA, before queuing';return'After queuing, before completion';};const getConnectionInfo=()=>{var _transaction$connecti,_transaction$connecti2;if(transaction.helo&&(_transaction$connecti=transaction.connection)!==null&&_transaction$connecti!==void 0&&_transaction$connecti.remote_host){return`Client \"${transaction.helo}\" from ${transaction.connection.remote_host}`;}else if(transaction.helo){return`Client \"${transaction.helo}\"`;}else if((_transaction$connecti2=transaction.connection)!==null&&_transaction$connecti2!==void 0&&_transaction$connecti2.remote_host){return`Client from ${transaction.connection.remote_host}`;}return'Unknown client';};return/*#__PURE__*/_jsxs(InfoCard,{title:\"Connection Details\",icon:Server,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-yellow-800\",children:\"Connection Lost\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-yellow-700 mt-1\",children:[getConnectionInfo(),\" disconnected \",getDisconnectionStage()]})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Client HELO\",value:transaction.helo||'Not provided'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Disconnection Stage\",value:getDisconnectionStage()}),/*#__PURE__*/_jsx(InfoRow,{label:\"Connection Duration\",value:formatProcessingTime(transaction.processing_time)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Disconnect Time\",value:formatDate(((_transaction$timestam4=transaction.timestamps)===null||_transaction$timestam4===void 0?void 0:_transaction$timestam4.disconnect)||transaction.timestamp)}),((_transaction$timestam5=transaction.timestamps)===null||_transaction$timestam5===void 0?void 0:_transaction$timestam5.connect)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Connected At\",value:formatDate(transaction.timestamps.connect)}),((_transaction$connecti3=transaction.connection)===null||_transaction$connecti3===void 0?void 0:(_transaction$connecti4=_transaction$connecti3.tls)===null||_transaction$connecti4===void 0?void 0:_transaction$connecti4.enabled)&&/*#__PURE__*/_jsx(InfoRow,{label:\"TLS Connection\",value:\"Yes\"})]})]});};const DeferredInfo=()=>{var _transaction$recipien,_transaction$recipien2,_transaction$recipien3,_transaction$recipien4;return/*#__PURE__*/_jsxs(InfoCard,{title:\"Deferral Details\",icon:Clock,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(Clock,{className:\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-yellow-800\",children:\"Email Deferred\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-yellow-700 mt-1\",children:transaction.defer_reason||'Email delivery was temporarily deferred'})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Defer Reason\",value:transaction.defer_reason||'N/A'}),transaction.recipient&&transaction.recipient.length>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Recipient\",value:formatEmail((_transaction$recipien=transaction.recipient[0])===null||_transaction$recipien===void 0?void 0:_transaction$recipien.original)}),((_transaction$recipien2=transaction.recipient[0])===null||_transaction$recipien2===void 0?void 0:_transaction$recipien2.dsn_code)&&/*#__PURE__*/_jsx(InfoRow,{label:\"DSN Code\",value:transaction.recipient[0].dsn_code}),((_transaction$recipien3=transaction.recipient[0])===null||_transaction$recipien3===void 0?void 0:_transaction$recipien3.dsn_msg)&&/*#__PURE__*/_jsx(InfoRow,{label:\"DSN Message\",value:transaction.recipient[0].dsn_msg}),((_transaction$recipien4=transaction.recipient[0])===null||_transaction$recipien4===void 0?void 0:_transaction$recipien4.dsn_status)&&/*#__PURE__*/_jsx(InfoRow,{label:\"DSN Status\",value:transaction.recipient[0].dsn_status})]}),/*#__PURE__*/_jsx(InfoRow,{label:\"Deferral Time\",value:formatDate(transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-4\",children:/*#__PURE__*/_jsxs(Link,{to:\"/transactions\",className:\"flex items-center text-sm font-medium text-gray-500 hover:text-gray-700\",children:[/*#__PURE__*/_jsx(ArrowLeft,{className:\"h-4 w-4 mr-1\"}),\"Back to Transactions\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:flex md:items-center md:justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\",children:\"Transaction Details\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:[\"Transaction ID: \",transaction.transaction_id]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 flex md:mt-0 md:ml-4\",children:/*#__PURE__*/_jsx(\"span\",{className:`badge ${getStatusBadge(transaction.status,transaction).className} text-base px-3 py-1`,children:getStatusBadge(transaction.status,transaction).label})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6 lg:grid-cols-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-2\",children:/*#__PURE__*/_jsx(InfoCard,{title:\"Message Information\",icon:Mail,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"From\",value:(_transaction$mail_fro=transaction.mail_from)!==null&&_transaction$mail_fro!==void 0&&_transaction$mail_fro.address?formatEmail(transaction.mail_from.address):(_transaction$sender=transaction.sender)!==null&&_transaction$sender!==void 0&&_transaction$sender.original?formatEmail(transaction.sender.original):'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"To\",value:(()=>{const recipients=getTransactionRecipients(transaction);return recipients.length>0?recipients.map(r=>formatEmail(r)).join(', '):'No recipients';})()}),/*#__PURE__*/_jsx(InfoRow,{label:\"Subject\",value:formatSubject((_transaction$message=transaction.message)===null||_transaction$message===void 0?void 0:_transaction$message.subject)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Message ID\",value:((_transaction$message2=transaction.message)===null||_transaction$message2===void 0?void 0:_transaction$message2.message_id)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Size\",value:formatFileSize((_transaction$message3=transaction.message)===null||_transaction$message3===void 0?void 0:_transaction$message3.size)}),/*#__PURE__*/_jsx(InfoRow,{label:\"HELO/EHLO\",value:transaction.helo||'N/A'})]})})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(InfoCard,{title:\"Timing Information\",icon:Clock,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Received\",value:formatDate(transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Relative\",value:formatRelativeTime(transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)}),((_transaction$timestam6=transaction.timestamps)===null||_transaction$timestam6===void 0?void 0:_transaction$timestam6.connect)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Connected\",value:formatDate(transaction.timestamps.connect,'HH:mm:ss.SSS')}),((_transaction$timestam7=transaction.timestamps)===null||_transaction$timestam7===void 0?void 0:_transaction$timestam7.disconnect)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Disconnected\",value:formatDate(transaction.timestamps.disconnect,'HH:mm:ss.SSS')})]})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6 lg:grid-cols-2\",children:[/*#__PURE__*/_jsx(InfoCard,{title:\"Connection Information\",icon:Server,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Remote IP\",value:((_transaction$connecti5=transaction.connection)===null||_transaction$connecti5===void 0?void 0:_transaction$connecti5.remote_ip)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Remote Host\",value:((_transaction$connecti6=transaction.connection)===null||_transaction$connecti6===void 0?void 0:_transaction$connecti6.remote_host)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Local IP\",value:((_transaction$connecti7=transaction.connection)===null||_transaction$connecti7===void 0?void 0:_transaction$connecti7.local_ip)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Local Port\",value:((_transaction$connecti8=transaction.connection)===null||_transaction$connecti8===void 0?void 0:_transaction$connecti8.local_port)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"TLS Enabled\",value:(_transaction$connecti9=transaction.connection)!==null&&_transaction$connecti9!==void 0&&(_transaction$connecti0=_transaction$connecti9.tls)!==null&&_transaction$connecti0!==void 0&&_transaction$connecti0.enabled?'Yes':'No'}),((_transaction$connecti1=transaction.connection)===null||_transaction$connecti1===void 0?void 0:(_transaction$connecti10=_transaction$connecti1.tls)===null||_transaction$connecti10===void 0?void 0:_transaction$connecti10.cipher)&&/*#__PURE__*/_jsx(InfoRow,{label:\"TLS Cipher\",value:transaction.connection.tls.cipher})]})}),/*#__PURE__*/_jsx(InfoCard,{title:\"Authentication\",icon:Shield,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"SPF\",value:((_transaction$authenti0=transaction.authentication)===null||_transaction$authenti0===void 0?void 0:_transaction$authenti0.spf)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DKIM\",value:((_transaction$authenti1=transaction.authentication)===null||_transaction$authenti1===void 0?void 0:_transaction$authenti1.dkim)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DMARC\",value:((_transaction$authenti10=transaction.authentication)===null||_transaction$authenti10===void 0?void 0:_transaction$authenti10.dmarc)||'N/A'})]})})]}),consolidatedStatus==='delivered'&&/*#__PURE__*/_jsx(DeliveredInfo,{}),consolidatedStatus==='blocked'&&/*#__PURE__*/_jsx(BlockedInfo,{}),consolidatedStatus==='deferred'&&/*#__PURE__*/_jsx(DeferredInfo,{}),consolidatedStatus==='failed'&&/*#__PURE__*/_jsx(FailedInfo,{}),consolidatedStatus==='disconnected'&&/*#__PURE__*/_jsx(DisconnectedInfo,{}),transaction.headers&&Object.keys(transaction.headers).length>0&&/*#__PURE__*/_jsx(InfoCard,{title:\"Email Headers\",icon:FileText,children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:Object.entries(transaction.headers).map(_ref3=>{let[key,value]=_ref3;return/*#__PURE__*/_jsxs(\"div\",{className:\"border-b border-gray-200 pb-3 last:border-b-0\",children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 mb-1 capitalize\",children:key.replace(/-/g,' ')}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded break-all\",children:formatHeaderValue(value)})]},key);})})}),transaction.timestamps&&/*#__PURE__*/_jsx(InfoCard,{title:\"Transaction Timeline\",icon:Calendar,children:/*#__PURE__*/_jsx(\"div\",{className:\"flow-root\",children:/*#__PURE__*/_jsx(\"ul\",{className:\"-mb-8\",children:Object.entries(transaction.timestamps).sort((_ref4,_ref5)=>{let[,a]=_ref4;let[,b]=_ref5;return new Date(a)-new Date(b);}).map((_ref6,index,array)=>{let[event,timestamp]=_ref6;return/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative pb-8\",children:[index!==array.length-1&&/*#__PURE__*/_jsx(\"span\",{className:\"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative flex space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white\",children:/*#__PURE__*/_jsx(Hash,{className:\"h-4 w-4 text-white\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 capitalize\",children:event.replace(/_/g,' ')})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-right text-sm whitespace-nowrap text-gray-500\",children:/*#__PURE__*/_jsx(\"time\",{dateTime:timestamp,children:formatDate(timestamp,'HH:mm:ss.SSS')})})]})]})]})},event);})})})}),transaction.errors&&transaction.errors.length>0&&/*#__PURE__*/_jsx(InfoCard,{title:\"Errors\",icon:AlertCircle,children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:transaction.errors.map((error,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-3\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-800\",children:error})},index))})})]});};export default TransactionDetail;", "map": {"version": 3, "names": ["React", "useParams", "Link", "ArrowLeft", "Mail", "Clock", "Server", "Shield", "FileText", "User", "Globe", "Calendar", "Hash", "AlertCircle", "useTransaction", "formatDate", "formatRelativeTime", "getStatusBadge", "consolidateTransactionStatus", "formatEmail", "formatSubject", "formatFileSize", "formatProcessingTime", "formatHeaderValue", "getTransactionRecipients", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "TransactionDetail", "_transaction$mail_fro", "_transaction$sender", "_transaction$message", "_transaction$message2", "_transaction$message3", "_transaction$timestam6", "_transaction$timestam7", "_transaction$connecti5", "_transaction$connecti6", "_transaction$connecti7", "_transaction$connecti8", "_transaction$connecti9", "_transaction$connecti0", "_transaction$connecti1", "_transaction$connecti10", "_transaction$authenti0", "_transaction$authenti1", "_transaction$authenti10", "id", "data", "transaction", "loading", "error", "consolidatedStatus", "className", "children", "to", "InfoCard", "_ref", "title", "icon", "Icon", "InfoRow", "_ref2", "label", "value", "DeliveredInfo", "_transaction$authenti", "_transaction$authenti2", "_transaction$authenti3", "_transaction$timestam", "status", "delivery_details", "Array", "isArray", "authentication", "spf", "result", "dkim", "dmarc", "processing_time", "timestamps", "queue", "BlockedInfo", "_transaction$authenti4", "_transaction$authenti5", "_transaction$authenti6", "_transaction$authenti7", "_transaction$authenti8", "_transaction$authenti9", "_transaction$timestam2", "rejection_reason", "disconnect", "timestamp", "FailedInfo", "_transaction$errors", "_transaction$errors2", "_transaction$timestam3", "errors", "length", "DisconnectedInfo", "_transaction$timestam4", "_transaction$timestam5", "_transaction$connecti3", "_transaction$connecti4", "getDisconnectionStage", "helo", "mail_from", "data_start", "data_complete", "getConnectionInfo", "_transaction$connecti", "_transaction$connecti2", "connection", "remote_host", "connect", "tls", "enabled", "DeferredInfo", "_transaction$recipien", "_transaction$recipien2", "_transaction$recipien3", "_transaction$recipien4", "defer_reason", "recipient", "original", "dsn_code", "dsn_msg", "dsn_status", "transaction_id", "address", "sender", "recipients", "map", "r", "join", "message", "subject", "message_id", "size", "remote_ip", "local_ip", "local_port", "cipher", "headers", "Object", "keys", "entries", "_ref3", "key", "replace", "sort", "_ref4", "_ref5", "a", "b", "Date", "_ref6", "index", "array", "event", "dateTime"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/TransactionDetail.js"], "sourcesContent": ["import React from 'react';\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\nimport { \n  ArrowLeft, \n  Mail, \n  Clock, \n  Server, \n  Shield, \n  FileText,\n  User,\n  Globe,\n  Calendar,\n  Hash,\n  AlertCircle\n} from 'lucide-react';\nimport { useTransaction } from '../hooks/useApi';\nimport {\n  formatDate,\n  formatRelativeTime,\n  getStatusBadge,\n  consolidateTransactionStatus,\n  formatEmail,\n  formatSubject,\n  formatFileSize,\n  formatProcessingTime,\n  formatHeaderValue,\n  getTransactionRecipients\n} from '../utils/formatters';\n\nconst TransactionDetail = () => {\n  const { id } = useParams();\n  const { data: transaction, loading, error } = useTransaction(id);\n\n  // Get consolidated status for conditional rendering\n  const consolidatedStatus = transaction ? consolidateTransactionStatus(transaction) : null;\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"loading-spinner mx-auto\"></div>\n        <p className=\"mt-4 text-sm text-gray-500\">Loading transaction details...</p>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <AlertCircle className=\"mx-auto h-12 w-12 text-red-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Error loading transaction</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">{error}</p>\n        <Link to=\"/transactions\" className=\"mt-4 btn-primary\">\n          Back to Transactions\n        </Link>\n      </div>\n    );\n  }\n\n  if (!transaction) {\n    return (\n      <div className=\"text-center py-12\">\n        <Mail className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Transaction not found</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">The requested transaction could not be found.</p>\n        <Link to=\"/transactions\" className=\"mt-4 btn-primary\">\n          Back to Transactions\n        </Link>\n      </div>\n    );\n  }\n\n  const InfoCard = ({ title, icon: Icon, children }) => (\n    <div className=\"card p-6\">\n      <div className=\"flex items-center mb-4\">\n        <Icon className=\"h-5 w-5 text-gray-400 mr-2\" />\n        <h3 className=\"text-lg font-medium text-gray-900\">{title}</h3>\n      </div>\n      {children}\n    </div>\n  );\n\n  const InfoRow = ({ label, value, className = '' }) => (\n    <div className={`flex justify-between py-2 ${className}`}>\n      <dt className=\"text-sm font-medium text-gray-500\">{label}</dt>\n      <dd className=\"text-sm text-gray-900 text-right\">{value}</dd>\n    </div>\n  );\n\n  // Status-specific information components\n  const DeliveredInfo = () => (\n    <InfoCard title=\"Delivery Information\" icon={Mail}>\n      <dl className=\"divide-y divide-gray-200\">\n        <InfoRow\n          label=\"Delivery Status\"\n          value={transaction.status === 'relayed' ? 'Successfully Relayed' : 'Successfully Delivered'}\n        />\n\n        {/* Show delivery details for outbound delivered emails */}\n        {transaction.delivery_details && Array.isArray(transaction.delivery_details) && (\n          <>\n            <InfoRow\n              label=\"Destination Server\"\n              value={transaction.delivery_details[0] || 'N/A'}\n            />\n            <InfoRow\n              label=\"Server IP\"\n              value={transaction.delivery_details[1] || 'N/A'}\n            />\n            <InfoRow\n              label=\"SMTP Response\"\n              value={transaction.delivery_details[2] || 'N/A'}\n            />\n            <InfoRow\n              label=\"Delivery Time\"\n              value={transaction.delivery_details[3] ? `${transaction.delivery_details[3]}s` : 'N/A'}\n            />\n            <InfoRow\n              label=\"Port\"\n              value={transaction.delivery_details[4] || 'N/A'}\n            />\n            <InfoRow\n              label=\"Protocol\"\n              value={transaction.delivery_details[5] || 'N/A'}\n            />\n          </>\n        )}\n\n        {/* Show authentication for SMTP transactions */}\n        {transaction.authentication && (\n          <>\n            <InfoRow\n              label=\"SPF Result\"\n              value={transaction.authentication.spf?.result || 'N/A'}\n            />\n            <InfoRow\n              label=\"DKIM Result\"\n              value={transaction.authentication.dkim?.result || 'N/A'}\n            />\n            <InfoRow\n              label=\"DMARC Result\"\n              value={transaction.authentication.dmarc?.result || 'N/A'}\n            />\n          </>\n        )}\n\n        <InfoRow\n          label=\"Processing Time\"\n          value={formatProcessingTime(transaction.processing_time)}\n        />\n\n        {transaction.timestamps?.queue && (\n          <InfoRow\n            label=\"Queued At\"\n            value={formatDate(transaction.timestamps.queue)}\n          />\n        )}\n      </dl>\n    </InfoCard>\n  );\n\n  const BlockedInfo = () => (\n    <InfoCard title=\"Rejection Details\" icon={Shield}>\n      <div className=\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\">\n        <div className=\"flex\">\n          <AlertCircle className=\"h-5 w-5 text-red-400 mr-2 mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-red-800\">Email Blocked</h4>\n            <p className=\"text-sm text-red-700 mt-1\">\n              {transaction.rejection_reason || 'Email was rejected by the server'}\n            </p>\n          </div>\n        </div>\n      </div>\n      <dl className=\"divide-y divide-gray-200\">\n        {transaction.authentication && (\n          <>\n            <InfoRow\n              label=\"SPF Check\"\n              value={\n                <span className={transaction.authentication.spf?.result === 'fail' ? 'text-red-600 font-medium' : ''}>\n                  {transaction.authentication.spf?.result || 'N/A'}\n                </span>\n              }\n            />\n            <InfoRow\n              label=\"DKIM Check\"\n              value={\n                <span className={transaction.authentication.dkim?.result === 'fail' ? 'text-red-600 font-medium' : ''}>\n                  {transaction.authentication.dkim?.result || 'N/A'}\n                </span>\n              }\n            />\n            <InfoRow\n              label=\"DMARC Check\"\n              value={\n                <span className={transaction.authentication.dmarc?.result === 'fail' ? 'text-red-600 font-medium' : ''}>\n                  {transaction.authentication.dmarc?.result || 'N/A'}\n                </span>\n              }\n            />\n          </>\n        )}\n        <InfoRow\n          label=\"Rejection Time\"\n          value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}\n        />\n      </dl>\n    </InfoCard>\n  );\n\n  const FailedInfo = () => (\n    <InfoCard title=\"Failure Details\" icon={AlertCircle}>\n      <div className=\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\">\n        <div className=\"flex\">\n          <AlertCircle className=\"h-5 w-5 text-red-400 mr-2 mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-red-800\">Technical Failure</h4>\n            <p className=\"text-sm text-red-700 mt-1\">\n              {transaction.errors?.length > 0\n                ? transaction.errors[0]\n                : 'Email processing failed due to technical issues'}\n            </p>\n          </div>\n        </div>\n      </div>\n      <dl className=\"divide-y divide-gray-200\">\n        {transaction.errors?.length > 0 && (\n          <InfoRow\n            label=\"Error Count\"\n            value={transaction.errors.length}\n          />\n        )}\n        <InfoRow\n          label=\"Failure Time\"\n          value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}\n        />\n        <InfoRow\n          label=\"Processing Time\"\n          value={formatProcessingTime(transaction.processing_time)}\n        />\n      </dl>\n    </InfoCard>\n  );\n\n  const DisconnectedInfo = () => {\n    const getDisconnectionStage = () => {\n      const timestamps = transaction.timestamps || {};\n\n      // Based on your real data: client connected, sent HELO, but disconnected before MAIL FROM\n      if (!timestamps.helo) return 'During initial connection (before HELO)';\n      if (!timestamps.mail_from) return 'After HELO, before MAIL FROM command';\n      if (!timestamps.data_start) return 'After MAIL FROM, before DATA command';\n      if (!timestamps.data_complete) return 'During DATA transmission';\n      if (!timestamps.queue) return 'After DATA, before queuing';\n      return 'After queuing, before completion';\n    };\n\n    const getConnectionInfo = () => {\n      if (transaction.helo && transaction.connection?.remote_host) {\n        return `Client \"${transaction.helo}\" from ${transaction.connection.remote_host}`;\n      } else if (transaction.helo) {\n        return `Client \"${transaction.helo}\"`;\n      } else if (transaction.connection?.remote_host) {\n        return `Client from ${transaction.connection.remote_host}`;\n      }\n      return 'Unknown client';\n    };\n\n    return (\n      <InfoCard title=\"Connection Details\" icon={Server}>\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\" />\n            <div>\n              <h4 className=\"text-sm font-medium text-yellow-800\">Connection Lost</h4>\n              <p className=\"text-sm text-yellow-700 mt-1\">\n                {getConnectionInfo()} disconnected {getDisconnectionStage()}\n              </p>\n            </div>\n          </div>\n        </div>\n        <dl className=\"divide-y divide-gray-200\">\n          <InfoRow\n            label=\"Client HELO\"\n            value={transaction.helo || 'Not provided'}\n          />\n          <InfoRow\n            label=\"Disconnection Stage\"\n            value={getDisconnectionStage()}\n          />\n          <InfoRow\n            label=\"Connection Duration\"\n            value={formatProcessingTime(transaction.processing_time)}\n          />\n          <InfoRow\n            label=\"Disconnect Time\"\n            value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}\n          />\n          {transaction.timestamps?.connect && (\n            <InfoRow\n              label=\"Connected At\"\n              value={formatDate(transaction.timestamps.connect)}\n            />\n          )}\n          {transaction.connection?.tls?.enabled && (\n            <InfoRow\n              label=\"TLS Connection\"\n              value=\"Yes\"\n            />\n          )}\n        </dl>\n      </InfoCard>\n    );\n  };\n\n  const DeferredInfo = () => (\n    <InfoCard title=\"Deferral Details\" icon={Clock}>\n      <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4\">\n        <div className=\"flex\">\n          <Clock className=\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-yellow-800\">Email Deferred</h4>\n            <p className=\"text-sm text-yellow-700 mt-1\">\n              {transaction.defer_reason || 'Email delivery was temporarily deferred'}\n            </p>\n          </div>\n        </div>\n      </div>\n      <dl className=\"divide-y divide-gray-200\">\n        <InfoRow\n          label=\"Defer Reason\"\n          value={transaction.defer_reason || 'N/A'}\n        />\n        {transaction.recipient && transaction.recipient.length > 0 && (\n          <>\n            <InfoRow\n              label=\"Recipient\"\n              value={formatEmail(transaction.recipient[0]?.original)}\n            />\n            {transaction.recipient[0]?.dsn_code && (\n              <InfoRow\n                label=\"DSN Code\"\n                value={transaction.recipient[0].dsn_code}\n              />\n            )}\n            {transaction.recipient[0]?.dsn_msg && (\n              <InfoRow\n                label=\"DSN Message\"\n                value={transaction.recipient[0].dsn_msg}\n              />\n            )}\n            {transaction.recipient[0]?.dsn_status && (\n              <InfoRow\n                label=\"DSN Status\"\n                value={transaction.recipient[0].dsn_status}\n              />\n            )}\n          </>\n        )}\n        <InfoRow\n          label=\"Deferral Time\"\n          value={formatDate(transaction.timestamp)}\n        />\n        <InfoRow\n          label=\"Processing Time\"\n          value={formatProcessingTime(transaction.processing_time)}\n        />\n      </dl>\n    </InfoCard>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4\">\n        <Link\n          to=\"/transactions\"\n          className=\"flex items-center text-sm font-medium text-gray-500 hover:text-gray-700\"\n        >\n          <ArrowLeft className=\"h-4 w-4 mr-1\" />\n          Back to Transactions\n        </Link>\n      </div>\n\n      <div className=\"md:flex md:items-center md:justify-between\">\n        <div className=\"flex-1 min-w-0\">\n          <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n            Transaction Details\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Transaction ID: {transaction.transaction_id}\n          </p>\n        </div>\n        <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n          <span className={`badge ${getStatusBadge(transaction.status, transaction).className} text-base px-3 py-1`}>\n            {getStatusBadge(transaction.status, transaction).label}\n          </span>\n        </div>\n      </div>\n\n      {/* Overview */}\n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\n        <div className=\"lg:col-span-2\">\n          <InfoCard title=\"Message Information\" icon={Mail}>\n            <dl className=\"divide-y divide-gray-200\">\n              <InfoRow\n                label=\"From\"\n                value={\n                  transaction.mail_from?.address\n                    ? formatEmail(transaction.mail_from.address)\n                    : transaction.sender?.original\n                      ? formatEmail(transaction.sender.original)\n                      : 'N/A'\n                }\n              />\n              <InfoRow\n                label=\"To\"\n                value={\n                  (() => {\n                    const recipients = getTransactionRecipients(transaction);\n                    return recipients.length > 0\n                      ? recipients.map(r => formatEmail(r)).join(', ')\n                      : 'No recipients';\n                  })()\n                }\n              />\n              <InfoRow \n                label=\"Subject\" \n                value={formatSubject(transaction.message?.subject)} \n              />\n              <InfoRow \n                label=\"Message ID\" \n                value={transaction.message?.message_id || 'N/A'} \n              />\n              <InfoRow \n                label=\"Size\" \n                value={formatFileSize(transaction.message?.size)} \n              />\n              <InfoRow \n                label=\"HELO/EHLO\" \n                value={transaction.helo || 'N/A'} \n              />\n            </dl>\n          </InfoCard>\n        </div>\n\n        <div>\n          <InfoCard title=\"Timing Information\" icon={Clock}>\n            <dl className=\"divide-y divide-gray-200\">\n              <InfoRow \n                label=\"Received\" \n                value={formatDate(transaction.timestamp)} \n              />\n              <InfoRow \n                label=\"Relative\" \n                value={formatRelativeTime(transaction.timestamp)} \n              />\n              <InfoRow \n                label=\"Processing Time\" \n                value={formatProcessingTime(transaction.processing_time)} \n              />\n              {transaction.timestamps?.connect && (\n                <InfoRow \n                  label=\"Connected\" \n                  value={formatDate(transaction.timestamps.connect, 'HH:mm:ss.SSS')} \n                />\n              )}\n              {transaction.timestamps?.disconnect && (\n                <InfoRow \n                  label=\"Disconnected\" \n                  value={formatDate(transaction.timestamps.disconnect, 'HH:mm:ss.SSS')} \n                />\n              )}\n            </dl>\n          </InfoCard>\n        </div>\n      </div>\n\n      {/* Connection & Authentication */}\n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n        <InfoCard title=\"Connection Information\" icon={Server}>\n          <dl className=\"divide-y divide-gray-200\">\n            <InfoRow \n              label=\"Remote IP\" \n              value={transaction.connection?.remote_ip || 'N/A'} \n            />\n            <InfoRow \n              label=\"Remote Host\" \n              value={transaction.connection?.remote_host || 'N/A'} \n            />\n            <InfoRow \n              label=\"Local IP\" \n              value={transaction.connection?.local_ip || 'N/A'} \n            />\n            <InfoRow \n              label=\"Local Port\" \n              value={transaction.connection?.local_port || 'N/A'} \n            />\n            <InfoRow \n              label=\"TLS Enabled\" \n              value={transaction.connection?.tls?.enabled ? 'Yes' : 'No'} \n            />\n            {transaction.connection?.tls?.cipher && (\n              <InfoRow \n                label=\"TLS Cipher\" \n                value={transaction.connection.tls.cipher} \n              />\n            )}\n          </dl>\n        </InfoCard>\n\n        <InfoCard title=\"Authentication\" icon={Shield}>\n          <dl className=\"divide-y divide-gray-200\">\n            <InfoRow \n              label=\"SPF\" \n              value={transaction.authentication?.spf || 'N/A'} \n            />\n            <InfoRow \n              label=\"DKIM\" \n              value={transaction.authentication?.dkim || 'N/A'} \n            />\n            <InfoRow \n              label=\"DMARC\" \n              value={transaction.authentication?.dmarc || 'N/A'} \n            />\n          </dl>\n        </InfoCard>\n      </div>\n\n      {/* Status-specific Information */}\n      {consolidatedStatus === 'delivered' && <DeliveredInfo />}\n      {consolidatedStatus === 'blocked' && <BlockedInfo />}\n      {consolidatedStatus === 'deferred' && <DeferredInfo />}\n      {consolidatedStatus === 'failed' && <FailedInfo />}\n      {consolidatedStatus === 'disconnected' && <DisconnectedInfo />}\n\n      {/* Headers */}\n      {transaction.headers && Object.keys(transaction.headers).length > 0 && (\n        <InfoCard title=\"Email Headers\" icon={FileText}>\n          <div className=\"space-y-3\">\n            {Object.entries(transaction.headers).map(([key, value]) => (\n              <div key={key} className=\"border-b border-gray-200 pb-3 last:border-b-0\">\n                <dt className=\"text-sm font-medium text-gray-500 mb-1 capitalize\">\n                  {key.replace(/-/g, ' ')}\n                </dt>\n                <dd className=\"text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded break-all\">\n                  {formatHeaderValue(value)}\n                </dd>\n              </div>\n            ))}\n          </div>\n        </InfoCard>\n      )}\n\n      {/* Timeline */}\n      {transaction.timestamps && (\n        <InfoCard title=\"Transaction Timeline\" icon={Calendar}>\n          <div className=\"flow-root\">\n            <ul className=\"-mb-8\">\n              {Object.entries(transaction.timestamps)\n                .sort(([,a], [,b]) => new Date(a) - new Date(b))\n                .map(([event, timestamp], index, array) => (\n                <li key={event}>\n                  <div className=\"relative pb-8\">\n                    {index !== array.length - 1 && (\n                      <span className=\"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200\" />\n                    )}\n                    <div className=\"relative flex space-x-3\">\n                      <div>\n                        <span className=\"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white\">\n                          <Hash className=\"h-4 w-4 text-white\" />\n                        </span>\n                      </div>\n                      <div className=\"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\">\n                        <div>\n                          <p className=\"text-sm text-gray-500 capitalize\">\n                            {event.replace(/_/g, ' ')}\n                          </p>\n                        </div>\n                        <div className=\"text-right text-sm whitespace-nowrap text-gray-500\">\n                          <time dateTime={timestamp}>\n                            {formatDate(timestamp, 'HH:mm:ss.SSS')}\n                          </time>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </InfoCard>\n      )}\n\n      {/* Errors */}\n      {transaction.errors && transaction.errors.length > 0 && (\n        <InfoCard title=\"Errors\" icon={AlertCircle}>\n          <div className=\"space-y-2\">\n            {transaction.errors.map((error, index) => (\n              <div key={index} className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                <p className=\"text-sm text-red-800\">{error}</p>\n              </div>\n            ))}\n          </div>\n        </InfoCard>\n      )}\n    </div>\n  );\n};\n\nexport default TransactionDetail;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,IAAI,KAAQ,kBAAkB,CAClD,OACEC,SAAS,CACTC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,QAAQ,CACRC,IAAI,CACJC,KAAK,CACLC,QAAQ,CACRC,IAAI,CACJC,WAAW,KACN,cAAc,CACrB,OAASC,cAAc,KAAQ,iBAAiB,CAChD,OACEC,UAAU,CACVC,kBAAkB,CAClBC,cAAc,CACdC,4BAA4B,CAC5BC,WAAW,CACXC,aAAa,CACbC,cAAc,CACdC,oBAAoB,CACpBC,iBAAiB,CACjBC,wBAAwB,KACnB,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7B,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,mBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAC9B,KAAM,CAAEC,EAAG,CAAC,CAAGjD,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAEkD,IAAI,CAAEC,WAAW,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGxC,cAAc,CAACoC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAAK,kBAAkB,CAAGH,WAAW,CAAGlC,4BAA4B,CAACkC,WAAW,CAAC,CAAG,IAAI,CAEzF,GAAIC,OAAO,CAAE,CACX,mBACEzB,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/B,IAAA,QAAK8B,SAAS,CAAC,yBAAyB,CAAM,CAAC,cAC/C9B,IAAA,MAAG8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,gCAA8B,CAAG,CAAC,EACzE,CAAC,CAEV,CAEA,GAAIH,KAAK,CAAE,CACT,mBACE1B,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/B,IAAA,CAACb,WAAW,EAAC2C,SAAS,CAAC,gCAAgC,CAAE,CAAC,cAC1D9B,IAAA,OAAI8B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cACrF/B,IAAA,MAAG8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEH,KAAK,CAAI,CAAC,cACrD5B,IAAA,CAACxB,IAAI,EAACwD,EAAE,CAAC,eAAe,CAACF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,sBAEtD,CAAM,CAAC,EACJ,CAAC,CAEV,CAEA,GAAI,CAACL,WAAW,CAAE,CAChB,mBACExB,KAAA,QAAK4B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/B,IAAA,CAACtB,IAAI,EAACoD,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACpD9B,IAAA,OAAI8B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACjF/B,IAAA,MAAG8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,+CAA6C,CAAG,CAAC,cAC3F/B,IAAA,CAACxB,IAAI,EAACwD,EAAE,CAAC,eAAe,CAACF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,sBAEtD,CAAM,CAAC,EACJ,CAAC,CAEV,CAEA,KAAM,CAAAE,QAAQ,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,IAAI,CAAEC,IAAI,CAAEN,QAAS,CAAC,CAAAG,IAAA,oBAC/ChC,KAAA,QAAK4B,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvB7B,KAAA,QAAK4B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC/B,IAAA,CAACqC,IAAI,EAACP,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC/C9B,IAAA,OAAI8B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEI,KAAK,CAAK,CAAC,EAC3D,CAAC,CACLJ,QAAQ,EACN,CAAC,EACP,CAED,KAAM,CAAAO,OAAO,CAAGC,KAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEX,SAAS,CAAG,EAAG,CAAC,CAAAS,KAAA,oBAC/CrC,KAAA,QAAK4B,SAAS,CAAE,6BAA6BA,SAAS,EAAG,CAAAC,QAAA,eACvD/B,IAAA,OAAI8B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAES,KAAK,CAAK,CAAC,cAC9DxC,IAAA,OAAI8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEU,KAAK,CAAK,CAAC,EAC1D,CAAC,EACP,CAED;AACA,KAAM,CAAAC,aAAa,CAAGA,CAAA,QAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,oBACpB9C,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,sBAAsB,CAACC,IAAI,CAAE1D,IAAK,CAAAqD,QAAA,cAChD7B,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAEf,WAAW,CAACqB,MAAM,GAAK,SAAS,CAAG,sBAAsB,CAAG,wBAAyB,CAC7F,CAAC,CAGDrB,WAAW,CAACsB,gBAAgB,EAAIC,KAAK,CAACC,OAAO,CAACxB,WAAW,CAACsB,gBAAgB,CAAC,eAC1E9C,KAAA,CAAAE,SAAA,EAAA2B,QAAA,eACE/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,oBAAoB,CAC1BC,KAAK,CAAEf,WAAW,CAACsB,gBAAgB,CAAC,CAAC,CAAC,EAAI,KAAM,CACjD,CAAC,cACFhD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAEf,WAAW,CAACsB,gBAAgB,CAAC,CAAC,CAAC,EAAI,KAAM,CACjD,CAAC,cACFhD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,eAAe,CACrBC,KAAK,CAAEf,WAAW,CAACsB,gBAAgB,CAAC,CAAC,CAAC,EAAI,KAAM,CACjD,CAAC,cACFhD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,eAAe,CACrBC,KAAK,CAAEf,WAAW,CAACsB,gBAAgB,CAAC,CAAC,CAAC,CAAG,GAAGtB,WAAW,CAACsB,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAG,KAAM,CACxF,CAAC,cACFhD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,MAAM,CACZC,KAAK,CAAEf,WAAW,CAACsB,gBAAgB,CAAC,CAAC,CAAC,EAAI,KAAM,CACjD,CAAC,cACFhD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAEf,WAAW,CAACsB,gBAAgB,CAAC,CAAC,CAAC,EAAI,KAAM,CACjD,CAAC,EACF,CACH,CAGAtB,WAAW,CAACyB,cAAc,eACzBjD,KAAA,CAAAE,SAAA,EAAA2B,QAAA,eACE/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAE,EAAAE,qBAAA,CAAAjB,WAAW,CAACyB,cAAc,CAACC,GAAG,UAAAT,qBAAA,iBAA9BA,qBAAA,CAAgCU,MAAM,GAAI,KAAM,CACxD,CAAC,cACFrD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAE,EAAAG,sBAAA,CAAAlB,WAAW,CAACyB,cAAc,CAACG,IAAI,UAAAV,sBAAA,iBAA/BA,sBAAA,CAAiCS,MAAM,GAAI,KAAM,CACzD,CAAC,cACFrD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAE,EAAAI,sBAAA,CAAAnB,WAAW,CAACyB,cAAc,CAACI,KAAK,UAAAV,sBAAA,iBAAhCA,sBAAA,CAAkCQ,MAAM,GAAI,KAAM,CAC1D,CAAC,EACF,CACH,cAEDrD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE7C,oBAAoB,CAAC8B,WAAW,CAAC8B,eAAe,CAAE,CAC1D,CAAC,CAED,EAAAV,qBAAA,CAAApB,WAAW,CAAC+B,UAAU,UAAAX,qBAAA,iBAAtBA,qBAAA,CAAwBY,KAAK,gBAC5B1D,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAEpD,UAAU,CAACqC,WAAW,CAAC+B,UAAU,CAACC,KAAK,CAAE,CACjD,CACF,EACC,CAAC,CACG,CAAC,EACZ,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,QAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAClBhE,KAAA,CAAC+B,QAAQ,EAACE,KAAK,CAAC,mBAAmB,CAACC,IAAI,CAAEvD,MAAO,CAAAkD,QAAA,eAC/C/B,IAAA,QAAK8B,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClE7B,KAAA,QAAK4B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB/B,IAAA,CAACb,WAAW,EAAC2C,SAAS,CAAC,kCAAkC,CAAE,CAAC,cAC5D5B,KAAA,QAAA6B,QAAA,eACE/B,IAAA,OAAI8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACnE/B,IAAA,MAAG8B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACrCL,WAAW,CAACyC,gBAAgB,EAAI,kCAAkC,CAClE,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACNjE,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,EACrCL,WAAW,CAACyB,cAAc,eACzBjD,KAAA,CAAAE,SAAA,EAAA2B,QAAA,eACE/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,cACHzC,IAAA,SAAM8B,SAAS,CAAE,EAAA8B,sBAAA,CAAAlC,WAAW,CAACyB,cAAc,CAACC,GAAG,UAAAQ,sBAAA,iBAA9BA,sBAAA,CAAgCP,MAAM,IAAK,MAAM,CAAG,0BAA0B,CAAG,EAAG,CAAAtB,QAAA,CAClG,EAAA8B,sBAAA,CAAAnC,WAAW,CAACyB,cAAc,CAACC,GAAG,UAAAS,sBAAA,iBAA9BA,sBAAA,CAAgCR,MAAM,GAAI,KAAK,CAC5C,CACP,CACF,CAAC,cACFrD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,cACHzC,IAAA,SAAM8B,SAAS,CAAE,EAAAgC,sBAAA,CAAApC,WAAW,CAACyB,cAAc,CAACG,IAAI,UAAAQ,sBAAA,iBAA/BA,sBAAA,CAAiCT,MAAM,IAAK,MAAM,CAAG,0BAA0B,CAAG,EAAG,CAAAtB,QAAA,CACnG,EAAAgC,sBAAA,CAAArC,WAAW,CAACyB,cAAc,CAACG,IAAI,UAAAS,sBAAA,iBAA/BA,sBAAA,CAAiCV,MAAM,GAAI,KAAK,CAC7C,CACP,CACF,CAAC,cACFrD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,cACHzC,IAAA,SAAM8B,SAAS,CAAE,EAAAkC,sBAAA,CAAAtC,WAAW,CAACyB,cAAc,CAACI,KAAK,UAAAS,sBAAA,iBAAhCA,sBAAA,CAAkCX,MAAM,IAAK,MAAM,CAAG,0BAA0B,CAAG,EAAG,CAAAtB,QAAA,CACpG,EAAAkC,sBAAA,CAAAvC,WAAW,CAACyB,cAAc,CAACI,KAAK,UAAAU,sBAAA,iBAAhCA,sBAAA,CAAkCZ,MAAM,GAAI,KAAK,CAC9C,CACP,CACF,CAAC,EACF,CACH,cACDrD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,gBAAgB,CACtBC,KAAK,CAAEpD,UAAU,CAAC,EAAA6E,sBAAA,CAAAxC,WAAW,CAAC+B,UAAU,UAAAS,sBAAA,iBAAtBA,sBAAA,CAAwBE,UAAU,GAAI1C,WAAW,CAAC2C,SAAS,CAAE,CAChF,CAAC,EACA,CAAC,EACG,CAAC,EACZ,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,QAAAC,mBAAA,CAAAC,oBAAA,CAAAC,sBAAA,oBACjBvE,KAAA,CAAC+B,QAAQ,EAACE,KAAK,CAAC,iBAAiB,CAACC,IAAI,CAAEjD,WAAY,CAAA4C,QAAA,eAClD/B,IAAA,QAAK8B,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClE7B,KAAA,QAAK4B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB/B,IAAA,CAACb,WAAW,EAAC2C,SAAS,CAAC,kCAAkC,CAAE,CAAC,cAC5D5B,KAAA,QAAA6B,QAAA,eACE/B,IAAA,OAAI8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACvE/B,IAAA,MAAG8B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACrC,EAAAwC,mBAAA,CAAA7C,WAAW,CAACgD,MAAM,UAAAH,mBAAA,iBAAlBA,mBAAA,CAAoBI,MAAM,EAAG,CAAC,CAC3BjD,WAAW,CAACgD,MAAM,CAAC,CAAC,CAAC,CACrB,iDAAiD,CACpD,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACNxE,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,EACrC,EAAAyC,oBAAA,CAAA9C,WAAW,CAACgD,MAAM,UAAAF,oBAAA,iBAAlBA,oBAAA,CAAoBG,MAAM,EAAG,CAAC,eAC7B3E,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAEf,WAAW,CAACgD,MAAM,CAACC,MAAO,CAClC,CACF,cACD3E,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAEpD,UAAU,CAAC,EAAAoF,sBAAA,CAAA/C,WAAW,CAAC+B,UAAU,UAAAgB,sBAAA,iBAAtBA,sBAAA,CAAwBL,UAAU,GAAI1C,WAAW,CAAC2C,SAAS,CAAE,CAChF,CAAC,cACFrE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE7C,oBAAoB,CAAC8B,WAAW,CAAC8B,eAAe,CAAE,CAC1D,CAAC,EACA,CAAC,EACG,CAAC,EACZ,CAED,KAAM,CAAAoB,gBAAgB,CAAGA,CAAA,GAAM,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC7B,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAxB,UAAU,CAAG/B,WAAW,CAAC+B,UAAU,EAAI,CAAC,CAAC,CAE/C;AACA,GAAI,CAACA,UAAU,CAACyB,IAAI,CAAE,MAAO,yCAAyC,CACtE,GAAI,CAACzB,UAAU,CAAC0B,SAAS,CAAE,MAAO,sCAAsC,CACxE,GAAI,CAAC1B,UAAU,CAAC2B,UAAU,CAAE,MAAO,sCAAsC,CACzE,GAAI,CAAC3B,UAAU,CAAC4B,aAAa,CAAE,MAAO,0BAA0B,CAChE,GAAI,CAAC5B,UAAU,CAACC,KAAK,CAAE,MAAO,4BAA4B,CAC1D,MAAO,kCAAkC,CAC3C,CAAC,CAED,KAAM,CAAA4B,iBAAiB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAC9B,GAAI9D,WAAW,CAACwD,IAAI,GAAAK,qBAAA,CAAI7D,WAAW,CAAC+D,UAAU,UAAAF,qBAAA,WAAtBA,qBAAA,CAAwBG,WAAW,CAAE,CAC3D,MAAO,WAAWhE,WAAW,CAACwD,IAAI,UAAUxD,WAAW,CAAC+D,UAAU,CAACC,WAAW,EAAE,CAClF,CAAC,IAAM,IAAIhE,WAAW,CAACwD,IAAI,CAAE,CAC3B,MAAO,WAAWxD,WAAW,CAACwD,IAAI,GAAG,CACvC,CAAC,IAAM,KAAAM,sBAAA,CAAI9D,WAAW,CAAC+D,UAAU,UAAAD,sBAAA,WAAtBA,sBAAA,CAAwBE,WAAW,CAAE,CAC9C,MAAO,eAAehE,WAAW,CAAC+D,UAAU,CAACC,WAAW,EAAE,CAC5D,CACA,MAAO,gBAAgB,CACzB,CAAC,CAED,mBACExF,KAAA,CAAC+B,QAAQ,EAACE,KAAK,CAAC,oBAAoB,CAACC,IAAI,CAAExD,MAAO,CAAAmD,QAAA,eAChD/B,IAAA,QAAK8B,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxE7B,KAAA,QAAK4B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB/B,IAAA,CAACb,WAAW,EAAC2C,SAAS,CAAC,qCAAqC,CAAE,CAAC,cAC/D5B,KAAA,QAAA6B,QAAA,eACE/B,IAAA,OAAI8B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACxE7B,KAAA,MAAG4B,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EACxCuD,iBAAiB,CAAC,CAAC,CAAC,gBAAc,CAACL,qBAAqB,CAAC,CAAC,EAC1D,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACN/E,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAEf,WAAW,CAACwD,IAAI,EAAI,cAAe,CAC3C,CAAC,cACFlF,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,qBAAqB,CAC3BC,KAAK,CAAEwC,qBAAqB,CAAC,CAAE,CAChC,CAAC,cACFjF,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,qBAAqB,CAC3BC,KAAK,CAAE7C,oBAAoB,CAAC8B,WAAW,CAAC8B,eAAe,CAAE,CAC1D,CAAC,cACFxD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAEpD,UAAU,CAAC,EAAAwF,sBAAA,CAAAnD,WAAW,CAAC+B,UAAU,UAAAoB,sBAAA,iBAAtBA,sBAAA,CAAwBT,UAAU,GAAI1C,WAAW,CAAC2C,SAAS,CAAE,CAChF,CAAC,CACD,EAAAS,sBAAA,CAAApD,WAAW,CAAC+B,UAAU,UAAAqB,sBAAA,iBAAtBA,sBAAA,CAAwBa,OAAO,gBAC9B3F,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAEpD,UAAU,CAACqC,WAAW,CAAC+B,UAAU,CAACkC,OAAO,CAAE,CACnD,CACF,CACA,EAAAZ,sBAAA,CAAArD,WAAW,CAAC+D,UAAU,UAAAV,sBAAA,kBAAAC,sBAAA,CAAtBD,sBAAA,CAAwBa,GAAG,UAAAZ,sBAAA,iBAA3BA,sBAAA,CAA6Ba,OAAO,gBACnC7F,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,gBAAgB,CACtBC,KAAK,CAAC,KAAK,CACZ,CACF,EACC,CAAC,EACG,CAAC,CAEf,CAAC,CAED,KAAM,CAAAqD,YAAY,CAAGA,CAAA,QAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBACnBhG,KAAA,CAAC+B,QAAQ,EAACE,KAAK,CAAC,kBAAkB,CAACC,IAAI,CAAEzD,KAAM,CAAAoD,QAAA,eAC7C/B,IAAA,QAAK8B,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxE7B,KAAA,QAAK4B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB/B,IAAA,CAACrB,KAAK,EAACmD,SAAS,CAAC,qCAAqC,CAAE,CAAC,cACzD5B,KAAA,QAAA6B,QAAA,eACE/B,IAAA,OAAI8B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cACvE/B,IAAA,MAAG8B,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACxCL,WAAW,CAACyE,YAAY,EAAI,yCAAyC,CACrE,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACNjG,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAEf,WAAW,CAACyE,YAAY,EAAI,KAAM,CAC1C,CAAC,CACDzE,WAAW,CAAC0E,SAAS,EAAI1E,WAAW,CAAC0E,SAAS,CAACzB,MAAM,CAAG,CAAC,eACxDzE,KAAA,CAAAE,SAAA,EAAA2B,QAAA,eACE/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAEhD,WAAW,EAAAsG,qBAAA,CAACrE,WAAW,CAAC0E,SAAS,CAAC,CAAC,CAAC,UAAAL,qBAAA,iBAAxBA,qBAAA,CAA0BM,QAAQ,CAAE,CACxD,CAAC,CACD,EAAAL,sBAAA,CAAAtE,WAAW,CAAC0E,SAAS,CAAC,CAAC,CAAC,UAAAJ,sBAAA,iBAAxBA,sBAAA,CAA0BM,QAAQ,gBACjCtG,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAEf,WAAW,CAAC0E,SAAS,CAAC,CAAC,CAAC,CAACE,QAAS,CAC1C,CACF,CACA,EAAAL,sBAAA,CAAAvE,WAAW,CAAC0E,SAAS,CAAC,CAAC,CAAC,UAAAH,sBAAA,iBAAxBA,sBAAA,CAA0BM,OAAO,gBAChCvG,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAEf,WAAW,CAAC0E,SAAS,CAAC,CAAC,CAAC,CAACG,OAAQ,CACzC,CACF,CACA,EAAAL,sBAAA,CAAAxE,WAAW,CAAC0E,SAAS,CAAC,CAAC,CAAC,UAAAF,sBAAA,iBAAxBA,sBAAA,CAA0BM,UAAU,gBACnCxG,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAEf,WAAW,CAAC0E,SAAS,CAAC,CAAC,CAAC,CAACI,UAAW,CAC5C,CACF,EACD,CACH,cACDxG,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,eAAe,CACrBC,KAAK,CAAEpD,UAAU,CAACqC,WAAW,CAAC2C,SAAS,CAAE,CAC1C,CAAC,cACFrE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE7C,oBAAoB,CAAC8B,WAAW,CAAC8B,eAAe,CAAE,CAC1D,CAAC,EACA,CAAC,EACG,CAAC,EACZ,CAED,mBACEtD,KAAA,QAAK4B,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB/B,IAAA,QAAK8B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C7B,KAAA,CAAC1B,IAAI,EACHwD,EAAE,CAAC,eAAe,CAClBF,SAAS,CAAC,yEAAyE,CAAAC,QAAA,eAEnF/B,IAAA,CAACvB,SAAS,EAACqD,SAAS,CAAC,cAAc,CAAE,CAAC,uBAExC,EAAM,CAAC,CACJ,CAAC,cAEN5B,KAAA,QAAK4B,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD7B,KAAA,QAAK4B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/B,IAAA,OAAI8B,SAAS,CAAC,oEAAoE,CAAAC,QAAA,CAAC,qBAEnF,CAAI,CAAC,cACL7B,KAAA,MAAG4B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,kBACxB,CAACL,WAAW,CAAC+E,cAAc,EAC1C,CAAC,EACD,CAAC,cACNzG,IAAA,QAAK8B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxC/B,IAAA,SAAM8B,SAAS,CAAE,SAASvC,cAAc,CAACmC,WAAW,CAACqB,MAAM,CAAErB,WAAW,CAAC,CAACI,SAAS,sBAAuB,CAAAC,QAAA,CACvGxC,cAAc,CAACmC,WAAW,CAACqB,MAAM,CAAErB,WAAW,CAAC,CAACc,KAAK,CAClD,CAAC,CACJ,CAAC,EACH,CAAC,cAGNtC,KAAA,QAAK4B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD/B,IAAA,QAAK8B,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B/B,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,qBAAqB,CAACC,IAAI,CAAE1D,IAAK,CAAAqD,QAAA,cAC/C7B,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,MAAM,CACZC,KAAK,CACH,CAAAnC,qBAAA,CAAAoB,WAAW,CAACyD,SAAS,UAAA7E,qBAAA,WAArBA,qBAAA,CAAuBoG,OAAO,CAC1BjH,WAAW,CAACiC,WAAW,CAACyD,SAAS,CAACuB,OAAO,CAAC,CAC1C,CAAAnG,mBAAA,CAAAmB,WAAW,CAACiF,MAAM,UAAApG,mBAAA,WAAlBA,mBAAA,CAAoB8F,QAAQ,CAC1B5G,WAAW,CAACiC,WAAW,CAACiF,MAAM,CAACN,QAAQ,CAAC,CACxC,KACP,CACF,CAAC,cACFrG,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,IAAI,CACVC,KAAK,CACH,CAAC,IAAM,CACL,KAAM,CAAAmE,UAAU,CAAG9G,wBAAwB,CAAC4B,WAAW,CAAC,CACxD,MAAO,CAAAkF,UAAU,CAACjC,MAAM,CAAG,CAAC,CACxBiC,UAAU,CAACC,GAAG,CAACC,CAAC,EAAIrH,WAAW,CAACqH,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAC9C,eAAe,CACrB,CAAC,EAAE,CACJ,CACF,CAAC,cACF/G,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,SAAS,CACfC,KAAK,CAAE/C,aAAa,EAAAc,oBAAA,CAACkB,WAAW,CAACsF,OAAO,UAAAxG,oBAAA,iBAAnBA,oBAAA,CAAqByG,OAAO,CAAE,CACpD,CAAC,cACFjH,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAE,EAAAhC,qBAAA,CAAAiB,WAAW,CAACsF,OAAO,UAAAvG,qBAAA,iBAAnBA,qBAAA,CAAqByG,UAAU,GAAI,KAAM,CACjD,CAAC,cACFlH,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,MAAM,CACZC,KAAK,CAAE9C,cAAc,EAAAe,qBAAA,CAACgB,WAAW,CAACsF,OAAO,UAAAtG,qBAAA,iBAAnBA,qBAAA,CAAqByG,IAAI,CAAE,CAClD,CAAC,cACFnH,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAEf,WAAW,CAACwD,IAAI,EAAI,KAAM,CAClC,CAAC,EACA,CAAC,CACG,CAAC,CACR,CAAC,cAENlF,IAAA,QAAA+B,QAAA,cACE/B,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,oBAAoB,CAACC,IAAI,CAAEzD,KAAM,CAAAoD,QAAA,cAC/C7B,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAEpD,UAAU,CAACqC,WAAW,CAAC2C,SAAS,CAAE,CAC1C,CAAC,cACFrE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAEnD,kBAAkB,CAACoC,WAAW,CAAC2C,SAAS,CAAE,CAClD,CAAC,cACFrE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE7C,oBAAoB,CAAC8B,WAAW,CAAC8B,eAAe,CAAE,CAC1D,CAAC,CACD,EAAA7C,sBAAA,CAAAe,WAAW,CAAC+B,UAAU,UAAA9C,sBAAA,iBAAtBA,sBAAA,CAAwBgF,OAAO,gBAC9B3F,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAEpD,UAAU,CAACqC,WAAW,CAAC+B,UAAU,CAACkC,OAAO,CAAE,cAAc,CAAE,CACnE,CACF,CACA,EAAA/E,sBAAA,CAAAc,WAAW,CAAC+B,UAAU,UAAA7C,sBAAA,iBAAtBA,sBAAA,CAAwBwD,UAAU,gBACjCpE,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAEpD,UAAU,CAACqC,WAAW,CAAC+B,UAAU,CAACW,UAAU,CAAE,cAAc,CAAE,CACtE,CACF,EACC,CAAC,CACG,CAAC,CACR,CAAC,EACH,CAAC,cAGNlE,KAAA,QAAK4B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpD/B,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,wBAAwB,CAACC,IAAI,CAAExD,MAAO,CAAAmD,QAAA,cACpD7B,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAE,EAAA5B,sBAAA,CAAAa,WAAW,CAAC+D,UAAU,UAAA5E,sBAAA,iBAAtBA,sBAAA,CAAwBuG,SAAS,GAAI,KAAM,CACnD,CAAC,cACFpH,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAE,EAAA3B,sBAAA,CAAAY,WAAW,CAAC+D,UAAU,UAAA3E,sBAAA,iBAAtBA,sBAAA,CAAwB4E,WAAW,GAAI,KAAM,CACrD,CAAC,cACF1F,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAE,EAAA1B,sBAAA,CAAAW,WAAW,CAAC+D,UAAU,UAAA1E,sBAAA,iBAAtBA,sBAAA,CAAwBsG,QAAQ,GAAI,KAAM,CAClD,CAAC,cACFrH,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAE,EAAAzB,sBAAA,CAAAU,WAAW,CAAC+D,UAAU,UAAAzE,sBAAA,iBAAtBA,sBAAA,CAAwBsG,UAAU,GAAI,KAAM,CACpD,CAAC,cACFtH,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAE,CAAAxB,sBAAA,CAAAS,WAAW,CAAC+D,UAAU,UAAAxE,sBAAA,YAAAC,sBAAA,CAAtBD,sBAAA,CAAwB2E,GAAG,UAAA1E,sBAAA,WAA3BA,sBAAA,CAA6B2E,OAAO,CAAG,KAAK,CAAG,IAAK,CAC5D,CAAC,CACD,EAAA1E,sBAAA,CAAAO,WAAW,CAAC+D,UAAU,UAAAtE,sBAAA,kBAAAC,uBAAA,CAAtBD,sBAAA,CAAwByE,GAAG,UAAAxE,uBAAA,iBAA3BA,uBAAA,CAA6BmG,MAAM,gBAClCvH,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAEf,WAAW,CAAC+D,UAAU,CAACG,GAAG,CAAC2B,MAAO,CAC1C,CACF,EACC,CAAC,CACG,CAAC,cAEXvH,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,gBAAgB,CAACC,IAAI,CAAEvD,MAAO,CAAAkD,QAAA,cAC5C7B,KAAA,OAAI4B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtC/B,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,KAAK,CACXC,KAAK,CAAE,EAAApB,sBAAA,CAAAK,WAAW,CAACyB,cAAc,UAAA9B,sBAAA,iBAA1BA,sBAAA,CAA4B+B,GAAG,GAAI,KAAM,CACjD,CAAC,cACFpD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,MAAM,CACZC,KAAK,CAAE,EAAAnB,sBAAA,CAAAI,WAAW,CAACyB,cAAc,UAAA7B,sBAAA,iBAA1BA,sBAAA,CAA4BgC,IAAI,GAAI,KAAM,CAClD,CAAC,cACFtD,IAAA,CAACsC,OAAO,EACNE,KAAK,CAAC,OAAO,CACbC,KAAK,CAAE,EAAAlB,uBAAA,CAAAG,WAAW,CAACyB,cAAc,UAAA5B,uBAAA,iBAA1BA,uBAAA,CAA4BgC,KAAK,GAAI,KAAM,CACnD,CAAC,EACA,CAAC,CACG,CAAC,EACR,CAAC,CAGL1B,kBAAkB,GAAK,WAAW,eAAI7B,IAAA,CAAC0C,aAAa,GAAE,CAAC,CACvDb,kBAAkB,GAAK,SAAS,eAAI7B,IAAA,CAAC2D,WAAW,GAAE,CAAC,CACnD9B,kBAAkB,GAAK,UAAU,eAAI7B,IAAA,CAAC8F,YAAY,GAAE,CAAC,CACrDjE,kBAAkB,GAAK,QAAQ,eAAI7B,IAAA,CAACsE,UAAU,GAAE,CAAC,CACjDzC,kBAAkB,GAAK,cAAc,eAAI7B,IAAA,CAAC4E,gBAAgB,GAAE,CAAC,CAG7DlD,WAAW,CAAC8F,OAAO,EAAIC,MAAM,CAACC,IAAI,CAAChG,WAAW,CAAC8F,OAAO,CAAC,CAAC7C,MAAM,CAAG,CAAC,eACjE3E,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,eAAe,CAACC,IAAI,CAAEtD,QAAS,CAAAiD,QAAA,cAC7C/B,IAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB0F,MAAM,CAACE,OAAO,CAACjG,WAAW,CAAC8F,OAAO,CAAC,CAACX,GAAG,CAACe,KAAA,MAAC,CAACC,GAAG,CAAEpF,KAAK,CAAC,CAAAmF,KAAA,oBACpD1H,KAAA,QAAe4B,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eACtE/B,IAAA,OAAI8B,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D8F,GAAG,CAACC,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CACrB,CAAC,cACL9H,IAAA,OAAI8B,SAAS,CAAC,kEAAkE,CAAAC,QAAA,CAC7ElC,iBAAiB,CAAC4C,KAAK,CAAC,CACvB,CAAC,GANGoF,GAOL,CAAC,EACP,CAAC,CACC,CAAC,CACE,CACX,CAGAnG,WAAW,CAAC+B,UAAU,eACrBzD,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,sBAAsB,CAACC,IAAI,CAAEnD,QAAS,CAAA8C,QAAA,cACpD/B,IAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB/B,IAAA,OAAI8B,SAAS,CAAC,OAAO,CAAAC,QAAA,CAClB0F,MAAM,CAACE,OAAO,CAACjG,WAAW,CAAC+B,UAAU,CAAC,CACpCsE,IAAI,CAAC,CAAAC,KAAA,CAAAC,KAAA,OAAC,EAAEC,CAAC,CAAC,CAAAF,KAAA,IAAE,EAAEG,CAAC,CAAC,CAAAF,KAAA,OAAK,IAAI,CAAAG,IAAI,CAACF,CAAC,CAAC,CAAG,GAAI,CAAAE,IAAI,CAACD,CAAC,CAAC,GAAC,CAC/CtB,GAAG,CAAC,CAAAwB,KAAA,CAAqBC,KAAK,CAAEC,KAAK,OAAhC,CAACC,KAAK,CAAEnE,SAAS,CAAC,CAAAgE,KAAA,oBACxBrI,IAAA,OAAA+B,QAAA,cACE7B,KAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3BuG,KAAK,GAAKC,KAAK,CAAC5D,MAAM,CAAG,CAAC,eACzB3E,IAAA,SAAM8B,SAAS,CAAC,uDAAuD,CAAE,CAC1E,cACD5B,KAAA,QAAK4B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC/B,IAAA,QAAA+B,QAAA,cACE/B,IAAA,SAAM8B,SAAS,CAAC,wFAAwF,CAAAC,QAAA,cACtG/B,IAAA,CAACd,IAAI,EAAC4C,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACnC,CAAC,CACJ,CAAC,cACN5B,KAAA,QAAK4B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnE/B,IAAA,QAAA+B,QAAA,cACE/B,IAAA,MAAG8B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC5CyG,KAAK,CAACV,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CACxB,CAAC,CACD,CAAC,cACN9H,IAAA,QAAK8B,SAAS,CAAC,oDAAoD,CAAAC,QAAA,cACjE/B,IAAA,SAAMyI,QAAQ,CAAEpE,SAAU,CAAAtC,QAAA,CACvB1C,UAAU,CAACgF,SAAS,CAAE,cAAc,CAAC,CAClC,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EAxBCmE,KAyBL,CAAC,EACN,CAAC,CACA,CAAC,CACF,CAAC,CACE,CACX,CAGA9G,WAAW,CAACgD,MAAM,EAAIhD,WAAW,CAACgD,MAAM,CAACC,MAAM,CAAG,CAAC,eAClD3E,IAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,QAAQ,CAACC,IAAI,CAAEjD,WAAY,CAAA4C,QAAA,cACzC/B,IAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBL,WAAW,CAACgD,MAAM,CAACmC,GAAG,CAAC,CAACjF,KAAK,CAAE0G,KAAK,gBACnCtI,IAAA,QAAiB8B,SAAS,CAAC,gDAAgD,CAAAC,QAAA,cACzE/B,IAAA,MAAG8B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAEH,KAAK,CAAI,CAAC,EADvC0G,KAEL,CACN,CAAC,CACC,CAAC,CACE,CACX,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}