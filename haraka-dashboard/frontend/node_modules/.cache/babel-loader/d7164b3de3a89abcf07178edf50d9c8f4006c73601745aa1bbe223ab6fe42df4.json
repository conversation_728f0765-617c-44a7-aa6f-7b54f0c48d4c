{"ast": null, "code": "import React from'react';import{useParams,Link}from'react-router-dom';import{ArrowLeft,Mail,Clock,Server,Shield,FileText,User,Globe,Calendar,Hash,AlertCircle}from'lucide-react';import{useTransaction}from'../hooks/useApi';import{formatDate,formatRelativeTime,getStatusBadge,consolidateTransactionStatus,formatEmail,formatSubject,formatFileSize,formatProcessingTime,formatHeaderValue}from'../utils/formatters';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const TransactionDetail=()=>{var _transaction$mail_fro,_transaction$sender,_transaction$rcpt_to,_transaction$recipien5,_transaction$message,_transaction$message2,_transaction$message3,_transaction$timestam6,_transaction$timestam7,_transaction$connecti,_transaction$connecti2,_transaction$connecti3,_transaction$connecti4,_transaction$connecti5,_transaction$connecti6,_transaction$connecti7,_transaction$connecti8,_transaction$authenti0,_transaction$authenti1,_transaction$authenti10;const{id}=useParams();const{data:transaction,loading,error}=useTransaction(id);// Get consolidated status for conditional rendering\nconst consolidatedStatus=transaction?consolidateTransactionStatus(transaction):null;if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner mx-auto\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-4 text-sm text-gray-500\",children:\"Loading transaction details...\"})]});}if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"mx-auto h-12 w-12 text-red-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"Error loading transaction\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:error}),/*#__PURE__*/_jsx(Link,{to:\"/transactions\",className:\"mt-4 btn-primary\",children:\"Back to Transactions\"})]});}if(!transaction){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(Mail,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"Transaction not found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"The requested transaction could not be found.\"}),/*#__PURE__*/_jsx(Link,{to:\"/transactions\",className:\"mt-4 btn-primary\",children:\"Back to Transactions\"})]});}const InfoCard=_ref=>{let{title,icon:Icon,children}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"card p-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(Icon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:title})]}),children]});};const InfoRow=_ref2=>{let{label,value,className=''}=_ref2;return/*#__PURE__*/_jsxs(\"div\",{className:`flex justify-between py-2 ${className}`,children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500\",children:label}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-sm text-gray-900 text-right\",children:value})]});};// Status-specific information components\nconst DeliveredInfo=()=>{var _transaction$authenti,_transaction$authenti2,_transaction$authenti3,_transaction$timestam;return/*#__PURE__*/_jsx(InfoCard,{title:\"Delivery Information\",icon:Mail,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Delivery Status\",value:transaction.status==='relayed'?'Successfully Relayed':'Successfully Delivered'}),transaction.authentication&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(InfoRow,{label:\"SPF Result\",value:((_transaction$authenti=transaction.authentication.spf)===null||_transaction$authenti===void 0?void 0:_transaction$authenti.result)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DKIM Result\",value:((_transaction$authenti2=transaction.authentication.dkim)===null||_transaction$authenti2===void 0?void 0:_transaction$authenti2.result)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DMARC Result\",value:((_transaction$authenti3=transaction.authentication.dmarc)===null||_transaction$authenti3===void 0?void 0:_transaction$authenti3.result)||'N/A'})]}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)}),((_transaction$timestam=transaction.timestamps)===null||_transaction$timestam===void 0?void 0:_transaction$timestam.queue)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Queued At\",value:formatDate(transaction.timestamps.queue)})]})});};const BlockedInfo=()=>{var _transaction$authenti4,_transaction$authenti5,_transaction$authenti6,_transaction$authenti7,_transaction$authenti8,_transaction$authenti9,_transaction$timestam2;return/*#__PURE__*/_jsxs(InfoCard,{title:\"Rejection Details\",icon:Shield,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"h-5 w-5 text-red-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-red-800\",children:\"Email Blocked\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-700 mt-1\",children:transaction.rejection_reason||'Email was rejected by the server'})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[transaction.authentication&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(InfoRow,{label:\"SPF Check\",value:/*#__PURE__*/_jsx(\"span\",{className:((_transaction$authenti4=transaction.authentication.spf)===null||_transaction$authenti4===void 0?void 0:_transaction$authenti4.result)==='fail'?'text-red-600 font-medium':'',children:((_transaction$authenti5=transaction.authentication.spf)===null||_transaction$authenti5===void 0?void 0:_transaction$authenti5.result)||'N/A'})}),/*#__PURE__*/_jsx(InfoRow,{label:\"DKIM Check\",value:/*#__PURE__*/_jsx(\"span\",{className:((_transaction$authenti6=transaction.authentication.dkim)===null||_transaction$authenti6===void 0?void 0:_transaction$authenti6.result)==='fail'?'text-red-600 font-medium':'',children:((_transaction$authenti7=transaction.authentication.dkim)===null||_transaction$authenti7===void 0?void 0:_transaction$authenti7.result)||'N/A'})}),/*#__PURE__*/_jsx(InfoRow,{label:\"DMARC Check\",value:/*#__PURE__*/_jsx(\"span\",{className:((_transaction$authenti8=transaction.authentication.dmarc)===null||_transaction$authenti8===void 0?void 0:_transaction$authenti8.result)==='fail'?'text-red-600 font-medium':'',children:((_transaction$authenti9=transaction.authentication.dmarc)===null||_transaction$authenti9===void 0?void 0:_transaction$authenti9.result)||'N/A'})})]}),/*#__PURE__*/_jsx(InfoRow,{label:\"Rejection Time\",value:formatDate(((_transaction$timestam2=transaction.timestamps)===null||_transaction$timestam2===void 0?void 0:_transaction$timestam2.disconnect)||transaction.timestamp)})]})]});};const FailedInfo=()=>{var _transaction$errors,_transaction$errors2,_transaction$timestam3;return/*#__PURE__*/_jsxs(InfoCard,{title:\"Failure Details\",icon:AlertCircle,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"h-5 w-5 text-red-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-red-800\",children:\"Technical Failure\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-700 mt-1\",children:((_transaction$errors=transaction.errors)===null||_transaction$errors===void 0?void 0:_transaction$errors.length)>0?transaction.errors[0]:'Email processing failed due to technical issues'})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[((_transaction$errors2=transaction.errors)===null||_transaction$errors2===void 0?void 0:_transaction$errors2.length)>0&&/*#__PURE__*/_jsx(InfoRow,{label:\"Error Count\",value:transaction.errors.length}),/*#__PURE__*/_jsx(InfoRow,{label:\"Failure Time\",value:formatDate(((_transaction$timestam3=transaction.timestamps)===null||_transaction$timestam3===void 0?void 0:_transaction$timestam3.disconnect)||transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)})]})]});};const DisconnectedInfo=()=>{var _transaction$timestam4,_transaction$timestam5;const getDisconnectionStage=()=>{const timestamps=transaction.timestamps||{};if(!timestamps.helo)return'During initial connection';if(!timestamps.mail_from)return'After HELO, before MAIL FROM';if(!timestamps.data_start)return'After MAIL FROM, before DATA';if(!timestamps.data_complete)return'During DATA transmission';return'After DATA, before completion';};return/*#__PURE__*/_jsxs(InfoCard,{title:\"Connection Details\",icon:Server,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(AlertCircle,{className:\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-yellow-800\",children:\"Connection Lost\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-yellow-700 mt-1\",children:[\"Client disconnected \",getDisconnectionStage()]})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Disconnection Stage\",value:getDisconnectionStage()}),/*#__PURE__*/_jsx(InfoRow,{label:\"Connection Duration\",value:formatProcessingTime(transaction.processing_time)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Disconnect Time\",value:formatDate(((_transaction$timestam4=transaction.timestamps)===null||_transaction$timestam4===void 0?void 0:_transaction$timestam4.disconnect)||transaction.timestamp)}),((_transaction$timestam5=transaction.timestamps)===null||_transaction$timestam5===void 0?void 0:_transaction$timestam5.connect)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Connected At\",value:formatDate(transaction.timestamps.connect)})]})]});};const DeferredInfo=()=>{var _transaction$recipien,_transaction$recipien2,_transaction$recipien3,_transaction$recipien4;return/*#__PURE__*/_jsxs(InfoCard,{title:\"Deferral Details\",icon:Clock,children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(Clock,{className:\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-yellow-800\",children:\"Email Deferred\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-yellow-700 mt-1\",children:transaction.defer_reason||'Email delivery was temporarily deferred'})]})]})}),/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Defer Reason\",value:transaction.defer_reason||'N/A'}),transaction.recipient&&transaction.recipient.length>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Recipient\",value:formatEmail((_transaction$recipien=transaction.recipient[0])===null||_transaction$recipien===void 0?void 0:_transaction$recipien.original)}),((_transaction$recipien2=transaction.recipient[0])===null||_transaction$recipien2===void 0?void 0:_transaction$recipien2.dsn_code)&&/*#__PURE__*/_jsx(InfoRow,{label:\"DSN Code\",value:transaction.recipient[0].dsn_code}),((_transaction$recipien3=transaction.recipient[0])===null||_transaction$recipien3===void 0?void 0:_transaction$recipien3.dsn_msg)&&/*#__PURE__*/_jsx(InfoRow,{label:\"DSN Message\",value:transaction.recipient[0].dsn_msg}),((_transaction$recipien4=transaction.recipient[0])===null||_transaction$recipien4===void 0?void 0:_transaction$recipien4.dsn_status)&&/*#__PURE__*/_jsx(InfoRow,{label:\"DSN Status\",value:transaction.recipient[0].dsn_status})]}),/*#__PURE__*/_jsx(InfoRow,{label:\"Deferral Time\",value:formatDate(transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-4\",children:/*#__PURE__*/_jsxs(Link,{to:\"/transactions\",className:\"flex items-center text-sm font-medium text-gray-500 hover:text-gray-700\",children:[/*#__PURE__*/_jsx(ArrowLeft,{className:\"h-4 w-4 mr-1\"}),\"Back to Transactions\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:flex md:items-center md:justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\",children:\"Transaction Details\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:[\"Transaction ID: \",transaction.transaction_id]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 flex md:mt-0 md:ml-4\",children:/*#__PURE__*/_jsx(\"span\",{className:`badge ${getStatusBadge(transaction.status,transaction).className} text-base px-3 py-1`,children:getStatusBadge(transaction.status,transaction).label})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6 lg:grid-cols-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"lg:col-span-2\",children:/*#__PURE__*/_jsx(InfoCard,{title:\"Message Information\",icon:Mail,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"From\",value:(_transaction$mail_fro=transaction.mail_from)!==null&&_transaction$mail_fro!==void 0&&_transaction$mail_fro.address?formatEmail(transaction.mail_from.address):(_transaction$sender=transaction.sender)!==null&&_transaction$sender!==void 0&&_transaction$sender.original?formatEmail(transaction.sender.original):'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"To\",value:((_transaction$rcpt_to=transaction.rcpt_to)===null||_transaction$rcpt_to===void 0?void 0:_transaction$rcpt_to.length)>0?transaction.rcpt_to.map(r=>formatEmail(r.address)).join(', '):((_transaction$recipien5=transaction.recipient)===null||_transaction$recipien5===void 0?void 0:_transaction$recipien5.length)>0?transaction.recipient.map(r=>formatEmail(r.original)).join(', '):'No recipients'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Subject\",value:formatSubject((_transaction$message=transaction.message)===null||_transaction$message===void 0?void 0:_transaction$message.subject)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Message ID\",value:((_transaction$message2=transaction.message)===null||_transaction$message2===void 0?void 0:_transaction$message2.message_id)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Size\",value:formatFileSize((_transaction$message3=transaction.message)===null||_transaction$message3===void 0?void 0:_transaction$message3.size)}),/*#__PURE__*/_jsx(InfoRow,{label:\"HELO/EHLO\",value:transaction.helo||'N/A'})]})})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(InfoCard,{title:\"Timing Information\",icon:Clock,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Received\",value:formatDate(transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Relative\",value:formatRelativeTime(transaction.timestamp)}),/*#__PURE__*/_jsx(InfoRow,{label:\"Processing Time\",value:formatProcessingTime(transaction.processing_time)}),((_transaction$timestam6=transaction.timestamps)===null||_transaction$timestam6===void 0?void 0:_transaction$timestam6.connect)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Connected\",value:formatDate(transaction.timestamps.connect,'HH:mm:ss.SSS')}),((_transaction$timestam7=transaction.timestamps)===null||_transaction$timestam7===void 0?void 0:_transaction$timestam7.disconnect)&&/*#__PURE__*/_jsx(InfoRow,{label:\"Disconnected\",value:formatDate(transaction.timestamps.disconnect,'HH:mm:ss.SSS')})]})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6 lg:grid-cols-2\",children:[/*#__PURE__*/_jsx(InfoCard,{title:\"Connection Information\",icon:Server,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"Remote IP\",value:((_transaction$connecti=transaction.connection)===null||_transaction$connecti===void 0?void 0:_transaction$connecti.remote_ip)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Remote Host\",value:((_transaction$connecti2=transaction.connection)===null||_transaction$connecti2===void 0?void 0:_transaction$connecti2.remote_host)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Local IP\",value:((_transaction$connecti3=transaction.connection)===null||_transaction$connecti3===void 0?void 0:_transaction$connecti3.local_ip)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"Local Port\",value:((_transaction$connecti4=transaction.connection)===null||_transaction$connecti4===void 0?void 0:_transaction$connecti4.local_port)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"TLS Enabled\",value:(_transaction$connecti5=transaction.connection)!==null&&_transaction$connecti5!==void 0&&(_transaction$connecti6=_transaction$connecti5.tls)!==null&&_transaction$connecti6!==void 0&&_transaction$connecti6.enabled?'Yes':'No'}),((_transaction$connecti7=transaction.connection)===null||_transaction$connecti7===void 0?void 0:(_transaction$connecti8=_transaction$connecti7.tls)===null||_transaction$connecti8===void 0?void 0:_transaction$connecti8.cipher)&&/*#__PURE__*/_jsx(InfoRow,{label:\"TLS Cipher\",value:transaction.connection.tls.cipher})]})}),/*#__PURE__*/_jsx(InfoCard,{title:\"Authentication\",icon:Shield,children:/*#__PURE__*/_jsxs(\"dl\",{className:\"divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(InfoRow,{label:\"SPF\",value:((_transaction$authenti0=transaction.authentication)===null||_transaction$authenti0===void 0?void 0:_transaction$authenti0.spf)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DKIM\",value:((_transaction$authenti1=transaction.authentication)===null||_transaction$authenti1===void 0?void 0:_transaction$authenti1.dkim)||'N/A'}),/*#__PURE__*/_jsx(InfoRow,{label:\"DMARC\",value:((_transaction$authenti10=transaction.authentication)===null||_transaction$authenti10===void 0?void 0:_transaction$authenti10.dmarc)||'N/A'})]})})]}),consolidatedStatus==='delivered'&&/*#__PURE__*/_jsx(DeliveredInfo,{}),consolidatedStatus==='blocked'&&/*#__PURE__*/_jsx(BlockedInfo,{}),consolidatedStatus==='deferred'&&/*#__PURE__*/_jsx(DeferredInfo,{}),consolidatedStatus==='failed'&&/*#__PURE__*/_jsx(FailedInfo,{}),consolidatedStatus==='disconnected'&&/*#__PURE__*/_jsx(DisconnectedInfo,{}),transaction.headers&&Object.keys(transaction.headers).length>0&&/*#__PURE__*/_jsx(InfoCard,{title:\"Email Headers\",icon:FileText,children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:Object.entries(transaction.headers).map(_ref3=>{let[key,value]=_ref3;return/*#__PURE__*/_jsxs(\"div\",{className:\"border-b border-gray-200 pb-3 last:border-b-0\",children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 mb-1 capitalize\",children:key.replace(/-/g,' ')}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded break-all\",children:formatHeaderValue(value)})]},key);})})}),transaction.timestamps&&/*#__PURE__*/_jsx(InfoCard,{title:\"Transaction Timeline\",icon:Calendar,children:/*#__PURE__*/_jsx(\"div\",{className:\"flow-root\",children:/*#__PURE__*/_jsx(\"ul\",{className:\"-mb-8\",children:Object.entries(transaction.timestamps).sort((_ref4,_ref5)=>{let[,a]=_ref4;let[,b]=_ref5;return new Date(a)-new Date(b);}).map((_ref6,index,array)=>{let[event,timestamp]=_ref6;return/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative pb-8\",children:[index!==array.length-1&&/*#__PURE__*/_jsx(\"span\",{className:\"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative flex space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white\",children:/*#__PURE__*/_jsx(Hash,{className:\"h-4 w-4 text-white\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 capitalize\",children:event.replace(/_/g,' ')})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-right text-sm whitespace-nowrap text-gray-500\",children:/*#__PURE__*/_jsx(\"time\",{dateTime:timestamp,children:formatDate(timestamp,'HH:mm:ss.SSS')})})]})]})]})},event);})})})}),transaction.errors&&transaction.errors.length>0&&/*#__PURE__*/_jsx(InfoCard,{title:\"Errors\",icon:AlertCircle,children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:transaction.errors.map((error,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-50 border border-red-200 rounded-md p-3\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-800\",children:error})},index))})})]});};export default TransactionDetail;", "map": {"version": 3, "names": ["React", "useParams", "Link", "ArrowLeft", "Mail", "Clock", "Server", "Shield", "FileText", "User", "Globe", "Calendar", "Hash", "AlertCircle", "useTransaction", "formatDate", "formatRelativeTime", "getStatusBadge", "consolidateTransactionStatus", "formatEmail", "formatSubject", "formatFileSize", "formatProcessingTime", "formatHeaderValue", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "TransactionDetail", "_transaction$mail_fro", "_transaction$sender", "_transaction$rcpt_to", "_transaction$recipien5", "_transaction$message", "_transaction$message2", "_transaction$message3", "_transaction$timestam6", "_transaction$timestam7", "_transaction$connecti", "_transaction$connecti2", "_transaction$connecti3", "_transaction$connecti4", "_transaction$connecti5", "_transaction$connecti6", "_transaction$connecti7", "_transaction$connecti8", "_transaction$authenti0", "_transaction$authenti1", "_transaction$authenti10", "id", "data", "transaction", "loading", "error", "consolidatedStatus", "className", "children", "to", "InfoCard", "_ref", "title", "icon", "Icon", "InfoRow", "_ref2", "label", "value", "DeliveredInfo", "_transaction$authenti", "_transaction$authenti2", "_transaction$authenti3", "_transaction$timestam", "status", "authentication", "spf", "result", "dkim", "dmarc", "processing_time", "timestamps", "queue", "BlockedInfo", "_transaction$authenti4", "_transaction$authenti5", "_transaction$authenti6", "_transaction$authenti7", "_transaction$authenti8", "_transaction$authenti9", "_transaction$timestam2", "rejection_reason", "disconnect", "timestamp", "FailedInfo", "_transaction$errors", "_transaction$errors2", "_transaction$timestam3", "errors", "length", "DisconnectedInfo", "_transaction$timestam4", "_transaction$timestam5", "getDisconnectionStage", "helo", "mail_from", "data_start", "data_complete", "connect", "DeferredInfo", "_transaction$recipien", "_transaction$recipien2", "_transaction$recipien3", "_transaction$recipien4", "defer_reason", "recipient", "original", "dsn_code", "dsn_msg", "dsn_status", "transaction_id", "address", "sender", "rcpt_to", "map", "r", "join", "message", "subject", "message_id", "size", "connection", "remote_ip", "remote_host", "local_ip", "local_port", "tls", "enabled", "cipher", "headers", "Object", "keys", "entries", "_ref3", "key", "replace", "sort", "_ref4", "_ref5", "a", "b", "Date", "_ref6", "index", "array", "event", "dateTime"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/TransactionDetail.js"], "sourcesContent": ["import React from 'react';\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\nimport { \n  ArrowLeft, \n  Mail, \n  Clock, \n  Server, \n  Shield, \n  FileText,\n  User,\n  Globe,\n  Calendar,\n  Hash,\n  AlertCircle\n} from 'lucide-react';\nimport { useTransaction } from '../hooks/useApi';\nimport {\n  formatDate,\n  formatRelativeTime,\n  getStatusBadge,\n  consolidateTransactionStatus,\n  formatEmail,\n  formatSubject,\n  formatFileSize,\n  formatProcessingTime,\n  formatHeaderValue\n} from '../utils/formatters';\n\nconst TransactionDetail = () => {\n  const { id } = useParams();\n  const { data: transaction, loading, error } = useTransaction(id);\n\n  // Get consolidated status for conditional rendering\n  const consolidatedStatus = transaction ? consolidateTransactionStatus(transaction) : null;\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"loading-spinner mx-auto\"></div>\n        <p className=\"mt-4 text-sm text-gray-500\">Loading transaction details...</p>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <AlertCircle className=\"mx-auto h-12 w-12 text-red-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Error loading transaction</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">{error}</p>\n        <Link to=\"/transactions\" className=\"mt-4 btn-primary\">\n          Back to Transactions\n        </Link>\n      </div>\n    );\n  }\n\n  if (!transaction) {\n    return (\n      <div className=\"text-center py-12\">\n        <Mail className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Transaction not found</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">The requested transaction could not be found.</p>\n        <Link to=\"/transactions\" className=\"mt-4 btn-primary\">\n          Back to Transactions\n        </Link>\n      </div>\n    );\n  }\n\n  const InfoCard = ({ title, icon: Icon, children }) => (\n    <div className=\"card p-6\">\n      <div className=\"flex items-center mb-4\">\n        <Icon className=\"h-5 w-5 text-gray-400 mr-2\" />\n        <h3 className=\"text-lg font-medium text-gray-900\">{title}</h3>\n      </div>\n      {children}\n    </div>\n  );\n\n  const InfoRow = ({ label, value, className = '' }) => (\n    <div className={`flex justify-between py-2 ${className}`}>\n      <dt className=\"text-sm font-medium text-gray-500\">{label}</dt>\n      <dd className=\"text-sm text-gray-900 text-right\">{value}</dd>\n    </div>\n  );\n\n  // Status-specific information components\n  const DeliveredInfo = () => (\n    <InfoCard title=\"Delivery Information\" icon={Mail}>\n      <dl className=\"divide-y divide-gray-200\">\n        <InfoRow\n          label=\"Delivery Status\"\n          value={transaction.status === 'relayed' ? 'Successfully Relayed' : 'Successfully Delivered'}\n        />\n        {transaction.authentication && (\n          <>\n            <InfoRow\n              label=\"SPF Result\"\n              value={transaction.authentication.spf?.result || 'N/A'}\n            />\n            <InfoRow\n              label=\"DKIM Result\"\n              value={transaction.authentication.dkim?.result || 'N/A'}\n            />\n            <InfoRow\n              label=\"DMARC Result\"\n              value={transaction.authentication.dmarc?.result || 'N/A'}\n            />\n          </>\n        )}\n        <InfoRow\n          label=\"Processing Time\"\n          value={formatProcessingTime(transaction.processing_time)}\n        />\n        {transaction.timestamps?.queue && (\n          <InfoRow\n            label=\"Queued At\"\n            value={formatDate(transaction.timestamps.queue)}\n          />\n        )}\n      </dl>\n    </InfoCard>\n  );\n\n  const BlockedInfo = () => (\n    <InfoCard title=\"Rejection Details\" icon={Shield}>\n      <div className=\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\">\n        <div className=\"flex\">\n          <AlertCircle className=\"h-5 w-5 text-red-400 mr-2 mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-red-800\">Email Blocked</h4>\n            <p className=\"text-sm text-red-700 mt-1\">\n              {transaction.rejection_reason || 'Email was rejected by the server'}\n            </p>\n          </div>\n        </div>\n      </div>\n      <dl className=\"divide-y divide-gray-200\">\n        {transaction.authentication && (\n          <>\n            <InfoRow\n              label=\"SPF Check\"\n              value={\n                <span className={transaction.authentication.spf?.result === 'fail' ? 'text-red-600 font-medium' : ''}>\n                  {transaction.authentication.spf?.result || 'N/A'}\n                </span>\n              }\n            />\n            <InfoRow\n              label=\"DKIM Check\"\n              value={\n                <span className={transaction.authentication.dkim?.result === 'fail' ? 'text-red-600 font-medium' : ''}>\n                  {transaction.authentication.dkim?.result || 'N/A'}\n                </span>\n              }\n            />\n            <InfoRow\n              label=\"DMARC Check\"\n              value={\n                <span className={transaction.authentication.dmarc?.result === 'fail' ? 'text-red-600 font-medium' : ''}>\n                  {transaction.authentication.dmarc?.result || 'N/A'}\n                </span>\n              }\n            />\n          </>\n        )}\n        <InfoRow\n          label=\"Rejection Time\"\n          value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}\n        />\n      </dl>\n    </InfoCard>\n  );\n\n  const FailedInfo = () => (\n    <InfoCard title=\"Failure Details\" icon={AlertCircle}>\n      <div className=\"bg-red-50 border border-red-200 rounded-md p-4 mb-4\">\n        <div className=\"flex\">\n          <AlertCircle className=\"h-5 w-5 text-red-400 mr-2 mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-red-800\">Technical Failure</h4>\n            <p className=\"text-sm text-red-700 mt-1\">\n              {transaction.errors?.length > 0\n                ? transaction.errors[0]\n                : 'Email processing failed due to technical issues'}\n            </p>\n          </div>\n        </div>\n      </div>\n      <dl className=\"divide-y divide-gray-200\">\n        {transaction.errors?.length > 0 && (\n          <InfoRow\n            label=\"Error Count\"\n            value={transaction.errors.length}\n          />\n        )}\n        <InfoRow\n          label=\"Failure Time\"\n          value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}\n        />\n        <InfoRow\n          label=\"Processing Time\"\n          value={formatProcessingTime(transaction.processing_time)}\n        />\n      </dl>\n    </InfoCard>\n  );\n\n  const DisconnectedInfo = () => {\n    const getDisconnectionStage = () => {\n      const timestamps = transaction.timestamps || {};\n      if (!timestamps.helo) return 'During initial connection';\n      if (!timestamps.mail_from) return 'After HELO, before MAIL FROM';\n      if (!timestamps.data_start) return 'After MAIL FROM, before DATA';\n      if (!timestamps.data_complete) return 'During DATA transmission';\n      return 'After DATA, before completion';\n    };\n\n    return (\n      <InfoCard title=\"Connection Details\" icon={Server}>\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\" />\n            <div>\n              <h4 className=\"text-sm font-medium text-yellow-800\">Connection Lost</h4>\n              <p className=\"text-sm text-yellow-700 mt-1\">\n                Client disconnected {getDisconnectionStage()}\n              </p>\n            </div>\n          </div>\n        </div>\n        <dl className=\"divide-y divide-gray-200\">\n          <InfoRow\n            label=\"Disconnection Stage\"\n            value={getDisconnectionStage()}\n          />\n          <InfoRow\n            label=\"Connection Duration\"\n            value={formatProcessingTime(transaction.processing_time)}\n          />\n          <InfoRow\n            label=\"Disconnect Time\"\n            value={formatDate(transaction.timestamps?.disconnect || transaction.timestamp)}\n          />\n          {transaction.timestamps?.connect && (\n            <InfoRow\n              label=\"Connected At\"\n              value={formatDate(transaction.timestamps.connect)}\n            />\n          )}\n        </dl>\n      </InfoCard>\n    );\n  };\n\n  const DeferredInfo = () => (\n    <InfoCard title=\"Deferral Details\" icon={Clock}>\n      <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4\">\n        <div className=\"flex\">\n          <Clock className=\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-yellow-800\">Email Deferred</h4>\n            <p className=\"text-sm text-yellow-700 mt-1\">\n              {transaction.defer_reason || 'Email delivery was temporarily deferred'}\n            </p>\n          </div>\n        </div>\n      </div>\n      <dl className=\"divide-y divide-gray-200\">\n        <InfoRow\n          label=\"Defer Reason\"\n          value={transaction.defer_reason || 'N/A'}\n        />\n        {transaction.recipient && transaction.recipient.length > 0 && (\n          <>\n            <InfoRow\n              label=\"Recipient\"\n              value={formatEmail(transaction.recipient[0]?.original)}\n            />\n            {transaction.recipient[0]?.dsn_code && (\n              <InfoRow\n                label=\"DSN Code\"\n                value={transaction.recipient[0].dsn_code}\n              />\n            )}\n            {transaction.recipient[0]?.dsn_msg && (\n              <InfoRow\n                label=\"DSN Message\"\n                value={transaction.recipient[0].dsn_msg}\n              />\n            )}\n            {transaction.recipient[0]?.dsn_status && (\n              <InfoRow\n                label=\"DSN Status\"\n                value={transaction.recipient[0].dsn_status}\n              />\n            )}\n          </>\n        )}\n        <InfoRow\n          label=\"Deferral Time\"\n          value={formatDate(transaction.timestamp)}\n        />\n        <InfoRow\n          label=\"Processing Time\"\n          value={formatProcessingTime(transaction.processing_time)}\n        />\n      </dl>\n    </InfoCard>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4\">\n        <Link\n          to=\"/transactions\"\n          className=\"flex items-center text-sm font-medium text-gray-500 hover:text-gray-700\"\n        >\n          <ArrowLeft className=\"h-4 w-4 mr-1\" />\n          Back to Transactions\n        </Link>\n      </div>\n\n      <div className=\"md:flex md:items-center md:justify-between\">\n        <div className=\"flex-1 min-w-0\">\n          <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n            Transaction Details\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Transaction ID: {transaction.transaction_id}\n          </p>\n        </div>\n        <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n          <span className={`badge ${getStatusBadge(transaction.status, transaction).className} text-base px-3 py-1`}>\n            {getStatusBadge(transaction.status, transaction).label}\n          </span>\n        </div>\n      </div>\n\n      {/* Overview */}\n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\n        <div className=\"lg:col-span-2\">\n          <InfoCard title=\"Message Information\" icon={Mail}>\n            <dl className=\"divide-y divide-gray-200\">\n              <InfoRow\n                label=\"From\"\n                value={\n                  transaction.mail_from?.address\n                    ? formatEmail(transaction.mail_from.address)\n                    : transaction.sender?.original\n                      ? formatEmail(transaction.sender.original)\n                      : 'N/A'\n                }\n              />\n              <InfoRow\n                label=\"To\"\n                value={\n                  transaction.rcpt_to?.length > 0\n                    ? transaction.rcpt_to.map(r => formatEmail(r.address)).join(', ')\n                    : transaction.recipient?.length > 0\n                      ? transaction.recipient.map(r => formatEmail(r.original)).join(', ')\n                      : 'No recipients'\n                }\n              />\n              <InfoRow \n                label=\"Subject\" \n                value={formatSubject(transaction.message?.subject)} \n              />\n              <InfoRow \n                label=\"Message ID\" \n                value={transaction.message?.message_id || 'N/A'} \n              />\n              <InfoRow \n                label=\"Size\" \n                value={formatFileSize(transaction.message?.size)} \n              />\n              <InfoRow \n                label=\"HELO/EHLO\" \n                value={transaction.helo || 'N/A'} \n              />\n            </dl>\n          </InfoCard>\n        </div>\n\n        <div>\n          <InfoCard title=\"Timing Information\" icon={Clock}>\n            <dl className=\"divide-y divide-gray-200\">\n              <InfoRow \n                label=\"Received\" \n                value={formatDate(transaction.timestamp)} \n              />\n              <InfoRow \n                label=\"Relative\" \n                value={formatRelativeTime(transaction.timestamp)} \n              />\n              <InfoRow \n                label=\"Processing Time\" \n                value={formatProcessingTime(transaction.processing_time)} \n              />\n              {transaction.timestamps?.connect && (\n                <InfoRow \n                  label=\"Connected\" \n                  value={formatDate(transaction.timestamps.connect, 'HH:mm:ss.SSS')} \n                />\n              )}\n              {transaction.timestamps?.disconnect && (\n                <InfoRow \n                  label=\"Disconnected\" \n                  value={formatDate(transaction.timestamps.disconnect, 'HH:mm:ss.SSS')} \n                />\n              )}\n            </dl>\n          </InfoCard>\n        </div>\n      </div>\n\n      {/* Connection & Authentication */}\n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n        <InfoCard title=\"Connection Information\" icon={Server}>\n          <dl className=\"divide-y divide-gray-200\">\n            <InfoRow \n              label=\"Remote IP\" \n              value={transaction.connection?.remote_ip || 'N/A'} \n            />\n            <InfoRow \n              label=\"Remote Host\" \n              value={transaction.connection?.remote_host || 'N/A'} \n            />\n            <InfoRow \n              label=\"Local IP\" \n              value={transaction.connection?.local_ip || 'N/A'} \n            />\n            <InfoRow \n              label=\"Local Port\" \n              value={transaction.connection?.local_port || 'N/A'} \n            />\n            <InfoRow \n              label=\"TLS Enabled\" \n              value={transaction.connection?.tls?.enabled ? 'Yes' : 'No'} \n            />\n            {transaction.connection?.tls?.cipher && (\n              <InfoRow \n                label=\"TLS Cipher\" \n                value={transaction.connection.tls.cipher} \n              />\n            )}\n          </dl>\n        </InfoCard>\n\n        <InfoCard title=\"Authentication\" icon={Shield}>\n          <dl className=\"divide-y divide-gray-200\">\n            <InfoRow \n              label=\"SPF\" \n              value={transaction.authentication?.spf || 'N/A'} \n            />\n            <InfoRow \n              label=\"DKIM\" \n              value={transaction.authentication?.dkim || 'N/A'} \n            />\n            <InfoRow \n              label=\"DMARC\" \n              value={transaction.authentication?.dmarc || 'N/A'} \n            />\n          </dl>\n        </InfoCard>\n      </div>\n\n      {/* Status-specific Information */}\n      {consolidatedStatus === 'delivered' && <DeliveredInfo />}\n      {consolidatedStatus === 'blocked' && <BlockedInfo />}\n      {consolidatedStatus === 'deferred' && <DeferredInfo />}\n      {consolidatedStatus === 'failed' && <FailedInfo />}\n      {consolidatedStatus === 'disconnected' && <DisconnectedInfo />}\n\n      {/* Headers */}\n      {transaction.headers && Object.keys(transaction.headers).length > 0 && (\n        <InfoCard title=\"Email Headers\" icon={FileText}>\n          <div className=\"space-y-3\">\n            {Object.entries(transaction.headers).map(([key, value]) => (\n              <div key={key} className=\"border-b border-gray-200 pb-3 last:border-b-0\">\n                <dt className=\"text-sm font-medium text-gray-500 mb-1 capitalize\">\n                  {key.replace(/-/g, ' ')}\n                </dt>\n                <dd className=\"text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded break-all\">\n                  {formatHeaderValue(value)}\n                </dd>\n              </div>\n            ))}\n          </div>\n        </InfoCard>\n      )}\n\n      {/* Timeline */}\n      {transaction.timestamps && (\n        <InfoCard title=\"Transaction Timeline\" icon={Calendar}>\n          <div className=\"flow-root\">\n            <ul className=\"-mb-8\">\n              {Object.entries(transaction.timestamps)\n                .sort(([,a], [,b]) => new Date(a) - new Date(b))\n                .map(([event, timestamp], index, array) => (\n                <li key={event}>\n                  <div className=\"relative pb-8\">\n                    {index !== array.length - 1 && (\n                      <span className=\"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200\" />\n                    )}\n                    <div className=\"relative flex space-x-3\">\n                      <div>\n                        <span className=\"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white\">\n                          <Hash className=\"h-4 w-4 text-white\" />\n                        </span>\n                      </div>\n                      <div className=\"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\">\n                        <div>\n                          <p className=\"text-sm text-gray-500 capitalize\">\n                            {event.replace(/_/g, ' ')}\n                          </p>\n                        </div>\n                        <div className=\"text-right text-sm whitespace-nowrap text-gray-500\">\n                          <time dateTime={timestamp}>\n                            {formatDate(timestamp, 'HH:mm:ss.SSS')}\n                          </time>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </InfoCard>\n      )}\n\n      {/* Errors */}\n      {transaction.errors && transaction.errors.length > 0 && (\n        <InfoCard title=\"Errors\" icon={AlertCircle}>\n          <div className=\"space-y-2\">\n            {transaction.errors.map((error, index) => (\n              <div key={index} className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                <p className=\"text-sm text-red-800\">{error}</p>\n              </div>\n            ))}\n          </div>\n        </InfoCard>\n      )}\n    </div>\n  );\n};\n\nexport default TransactionDetail;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,IAAI,KAAQ,kBAAkB,CAClD,OACEC,SAAS,CACTC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,QAAQ,CACRC,IAAI,CACJC,KAAK,CACLC,QAAQ,CACRC,IAAI,CACJC,WAAW,KACN,cAAc,CACrB,OAASC,cAAc,KAAQ,iBAAiB,CAChD,OACEC,UAAU,CACVC,kBAAkB,CAClBC,cAAc,CACdC,4BAA4B,CAC5BC,WAAW,CACXC,aAAa,CACbC,cAAc,CACdC,oBAAoB,CACpBC,iBAAiB,KACZ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7B,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,mBAAA,CAAAC,oBAAA,CAAAC,sBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAC9B,KAAM,CAAEC,EAAG,CAAC,CAAGlD,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAEmD,IAAI,CAAEC,WAAW,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGzC,cAAc,CAACqC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAAK,kBAAkB,CAAGH,WAAW,CAAGnC,4BAA4B,CAACmC,WAAW,CAAC,CAAG,IAAI,CAEzF,GAAIC,OAAO,CAAE,CACX,mBACE3B,KAAA,QAAK8B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjC,IAAA,QAAKgC,SAAS,CAAC,yBAAyB,CAAM,CAAC,cAC/ChC,IAAA,MAAGgC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,gCAA8B,CAAG,CAAC,EACzE,CAAC,CAEV,CAEA,GAAIH,KAAK,CAAE,CACT,mBACE5B,KAAA,QAAK8B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjC,IAAA,CAACZ,WAAW,EAAC4C,SAAS,CAAC,gCAAgC,CAAE,CAAC,cAC1DhC,IAAA,OAAIgC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cACrFjC,IAAA,MAAGgC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEH,KAAK,CAAI,CAAC,cACrD9B,IAAA,CAACvB,IAAI,EAACyD,EAAE,CAAC,eAAe,CAACF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,sBAEtD,CAAM,CAAC,EACJ,CAAC,CAEV,CAEA,GAAI,CAACL,WAAW,CAAE,CAChB,mBACE1B,KAAA,QAAK8B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjC,IAAA,CAACrB,IAAI,EAACqD,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACpDhC,IAAA,OAAIgC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACjFjC,IAAA,MAAGgC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,+CAA6C,CAAG,CAAC,cAC3FjC,IAAA,CAACvB,IAAI,EAACyD,EAAE,CAAC,eAAe,CAACF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,sBAEtD,CAAM,CAAC,EACJ,CAAC,CAEV,CAEA,KAAM,CAAAE,QAAQ,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,IAAI,CAAEC,IAAI,CAAEN,QAAS,CAAC,CAAAG,IAAA,oBAC/ClC,KAAA,QAAK8B,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvB/B,KAAA,QAAK8B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCjC,IAAA,CAACuC,IAAI,EAACP,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC/ChC,IAAA,OAAIgC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEI,KAAK,CAAK,CAAC,EAC3D,CAAC,CACLJ,QAAQ,EACN,CAAC,EACP,CAED,KAAM,CAAAO,OAAO,CAAGC,KAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEX,SAAS,CAAG,EAAG,CAAC,CAAAS,KAAA,oBAC/CvC,KAAA,QAAK8B,SAAS,CAAE,6BAA6BA,SAAS,EAAG,CAAAC,QAAA,eACvDjC,IAAA,OAAIgC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAES,KAAK,CAAK,CAAC,cAC9D1C,IAAA,OAAIgC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEU,KAAK,CAAK,CAAC,EAC1D,CAAC,EACP,CAED;AACA,KAAM,CAAAC,aAAa,CAAGA,CAAA,QAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,oBACpBhD,IAAA,CAACmC,QAAQ,EAACE,KAAK,CAAC,sBAAsB,CAACC,IAAI,CAAE3D,IAAK,CAAAsD,QAAA,cAChD/B,KAAA,OAAI8B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtCjC,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAEf,WAAW,CAACqB,MAAM,GAAK,SAAS,CAAG,sBAAsB,CAAG,wBAAyB,CAC7F,CAAC,CACDrB,WAAW,CAACsB,cAAc,eACzBhD,KAAA,CAAAE,SAAA,EAAA6B,QAAA,eACEjC,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAE,EAAAE,qBAAA,CAAAjB,WAAW,CAACsB,cAAc,CAACC,GAAG,UAAAN,qBAAA,iBAA9BA,qBAAA,CAAgCO,MAAM,GAAI,KAAM,CACxD,CAAC,cACFpD,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAE,EAAAG,sBAAA,CAAAlB,WAAW,CAACsB,cAAc,CAACG,IAAI,UAAAP,sBAAA,iBAA/BA,sBAAA,CAAiCM,MAAM,GAAI,KAAM,CACzD,CAAC,cACFpD,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAE,EAAAI,sBAAA,CAAAnB,WAAW,CAACsB,cAAc,CAACI,KAAK,UAAAP,sBAAA,iBAAhCA,sBAAA,CAAkCK,MAAM,GAAI,KAAM,CAC1D,CAAC,EACF,CACH,cACDpD,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE9C,oBAAoB,CAAC+B,WAAW,CAAC2B,eAAe,CAAE,CAC1D,CAAC,CACD,EAAAP,qBAAA,CAAApB,WAAW,CAAC4B,UAAU,UAAAR,qBAAA,iBAAtBA,qBAAA,CAAwBS,KAAK,gBAC5BzD,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAErD,UAAU,CAACsC,WAAW,CAAC4B,UAAU,CAACC,KAAK,CAAE,CACjD,CACF,EACC,CAAC,CACG,CAAC,EACZ,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,QAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAClB/D,KAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,mBAAmB,CAACC,IAAI,CAAExD,MAAO,CAAAmD,QAAA,eAC/CjC,IAAA,QAAKgC,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClE/B,KAAA,QAAK8B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBjC,IAAA,CAACZ,WAAW,EAAC4C,SAAS,CAAC,kCAAkC,CAAE,CAAC,cAC5D9B,KAAA,QAAA+B,QAAA,eACEjC,IAAA,OAAIgC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACnEjC,IAAA,MAAGgC,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACrCL,WAAW,CAACsC,gBAAgB,EAAI,kCAAkC,CAClE,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACNhE,KAAA,OAAI8B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,EACrCL,WAAW,CAACsB,cAAc,eACzBhD,KAAA,CAAAE,SAAA,EAAA6B,QAAA,eACEjC,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,cACH3C,IAAA,SAAMgC,SAAS,CAAE,EAAA2B,sBAAA,CAAA/B,WAAW,CAACsB,cAAc,CAACC,GAAG,UAAAQ,sBAAA,iBAA9BA,sBAAA,CAAgCP,MAAM,IAAK,MAAM,CAAG,0BAA0B,CAAG,EAAG,CAAAnB,QAAA,CAClG,EAAA2B,sBAAA,CAAAhC,WAAW,CAACsB,cAAc,CAACC,GAAG,UAAAS,sBAAA,iBAA9BA,sBAAA,CAAgCR,MAAM,GAAI,KAAK,CAC5C,CACP,CACF,CAAC,cACFpD,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,cACH3C,IAAA,SAAMgC,SAAS,CAAE,EAAA6B,sBAAA,CAAAjC,WAAW,CAACsB,cAAc,CAACG,IAAI,UAAAQ,sBAAA,iBAA/BA,sBAAA,CAAiCT,MAAM,IAAK,MAAM,CAAG,0BAA0B,CAAG,EAAG,CAAAnB,QAAA,CACnG,EAAA6B,sBAAA,CAAAlC,WAAW,CAACsB,cAAc,CAACG,IAAI,UAAAS,sBAAA,iBAA/BA,sBAAA,CAAiCV,MAAM,GAAI,KAAK,CAC7C,CACP,CACF,CAAC,cACFpD,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,cACH3C,IAAA,SAAMgC,SAAS,CAAE,EAAA+B,sBAAA,CAAAnC,WAAW,CAACsB,cAAc,CAACI,KAAK,UAAAS,sBAAA,iBAAhCA,sBAAA,CAAkCX,MAAM,IAAK,MAAM,CAAG,0BAA0B,CAAG,EAAG,CAAAnB,QAAA,CACpG,EAAA+B,sBAAA,CAAApC,WAAW,CAACsB,cAAc,CAACI,KAAK,UAAAU,sBAAA,iBAAhCA,sBAAA,CAAkCZ,MAAM,GAAI,KAAK,CAC9C,CACP,CACF,CAAC,EACF,CACH,cACDpD,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,gBAAgB,CACtBC,KAAK,CAAErD,UAAU,CAAC,EAAA2E,sBAAA,CAAArC,WAAW,CAAC4B,UAAU,UAAAS,sBAAA,iBAAtBA,sBAAA,CAAwBE,UAAU,GAAIvC,WAAW,CAACwC,SAAS,CAAE,CAChF,CAAC,EACA,CAAC,EACG,CAAC,EACZ,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,QAAAC,mBAAA,CAAAC,oBAAA,CAAAC,sBAAA,oBACjBtE,KAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,iBAAiB,CAACC,IAAI,CAAElD,WAAY,CAAA6C,QAAA,eAClDjC,IAAA,QAAKgC,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClE/B,KAAA,QAAK8B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBjC,IAAA,CAACZ,WAAW,EAAC4C,SAAS,CAAC,kCAAkC,CAAE,CAAC,cAC5D9B,KAAA,QAAA+B,QAAA,eACEjC,IAAA,OAAIgC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACvEjC,IAAA,MAAGgC,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACrC,EAAAqC,mBAAA,CAAA1C,WAAW,CAAC6C,MAAM,UAAAH,mBAAA,iBAAlBA,mBAAA,CAAoBI,MAAM,EAAG,CAAC,CAC3B9C,WAAW,CAAC6C,MAAM,CAAC,CAAC,CAAC,CACrB,iDAAiD,CACpD,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACNvE,KAAA,OAAI8B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,EACrC,EAAAsC,oBAAA,CAAA3C,WAAW,CAAC6C,MAAM,UAAAF,oBAAA,iBAAlBA,oBAAA,CAAoBG,MAAM,EAAG,CAAC,eAC7B1E,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAEf,WAAW,CAAC6C,MAAM,CAACC,MAAO,CAClC,CACF,cACD1E,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAErD,UAAU,CAAC,EAAAkF,sBAAA,CAAA5C,WAAW,CAAC4B,UAAU,UAAAgB,sBAAA,iBAAtBA,sBAAA,CAAwBL,UAAU,GAAIvC,WAAW,CAACwC,SAAS,CAAE,CAChF,CAAC,cACFpE,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE9C,oBAAoB,CAAC+B,WAAW,CAAC2B,eAAe,CAAE,CAC1D,CAAC,EACA,CAAC,EACG,CAAC,EACZ,CAED,KAAM,CAAAoB,gBAAgB,CAAGA,CAAA,GAAM,KAAAC,sBAAA,CAAAC,sBAAA,CAC7B,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAtB,UAAU,CAAG5B,WAAW,CAAC4B,UAAU,EAAI,CAAC,CAAC,CAC/C,GAAI,CAACA,UAAU,CAACuB,IAAI,CAAE,MAAO,2BAA2B,CACxD,GAAI,CAACvB,UAAU,CAACwB,SAAS,CAAE,MAAO,8BAA8B,CAChE,GAAI,CAACxB,UAAU,CAACyB,UAAU,CAAE,MAAO,8BAA8B,CACjE,GAAI,CAACzB,UAAU,CAAC0B,aAAa,CAAE,MAAO,0BAA0B,CAChE,MAAO,+BAA+B,CACxC,CAAC,CAED,mBACEhF,KAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,oBAAoB,CAACC,IAAI,CAAEzD,MAAO,CAAAoD,QAAA,eAChDjC,IAAA,QAAKgC,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxE/B,KAAA,QAAK8B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBjC,IAAA,CAACZ,WAAW,EAAC4C,SAAS,CAAC,qCAAqC,CAAE,CAAC,cAC/D9B,KAAA,QAAA+B,QAAA,eACEjC,IAAA,OAAIgC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACxE/B,KAAA,MAAG8B,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,sBACtB,CAAC6C,qBAAqB,CAAC,CAAC,EAC3C,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACN5E,KAAA,OAAI8B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtCjC,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,qBAAqB,CAC3BC,KAAK,CAAEmC,qBAAqB,CAAC,CAAE,CAChC,CAAC,cACF9E,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,qBAAqB,CAC3BC,KAAK,CAAE9C,oBAAoB,CAAC+B,WAAW,CAAC2B,eAAe,CAAE,CAC1D,CAAC,cACFvD,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAErD,UAAU,CAAC,EAAAsF,sBAAA,CAAAhD,WAAW,CAAC4B,UAAU,UAAAoB,sBAAA,iBAAtBA,sBAAA,CAAwBT,UAAU,GAAIvC,WAAW,CAACwC,SAAS,CAAE,CAChF,CAAC,CACD,EAAAS,sBAAA,CAAAjD,WAAW,CAAC4B,UAAU,UAAAqB,sBAAA,iBAAtBA,sBAAA,CAAwBM,OAAO,gBAC9BnF,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAErD,UAAU,CAACsC,WAAW,CAAC4B,UAAU,CAAC2B,OAAO,CAAE,CACnD,CACF,EACC,CAAC,EACG,CAAC,CAEf,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,QAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBACnBtF,KAAA,CAACiC,QAAQ,EAACE,KAAK,CAAC,kBAAkB,CAACC,IAAI,CAAE1D,KAAM,CAAAqD,QAAA,eAC7CjC,IAAA,QAAKgC,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxE/B,KAAA,QAAK8B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBjC,IAAA,CAACpB,KAAK,EAACoD,SAAS,CAAC,qCAAqC,CAAE,CAAC,cACzD9B,KAAA,QAAA+B,QAAA,eACEjC,IAAA,OAAIgC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cACvEjC,IAAA,MAAGgC,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACxCL,WAAW,CAAC6D,YAAY,EAAI,yCAAyC,CACrE,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cACNvF,KAAA,OAAI8B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtCjC,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAEf,WAAW,CAAC6D,YAAY,EAAI,KAAM,CAC1C,CAAC,CACD7D,WAAW,CAAC8D,SAAS,EAAI9D,WAAW,CAAC8D,SAAS,CAAChB,MAAM,CAAG,CAAC,eACxDxE,KAAA,CAAAE,SAAA,EAAA6B,QAAA,eACEjC,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAEjD,WAAW,EAAA2F,qBAAA,CAACzD,WAAW,CAAC8D,SAAS,CAAC,CAAC,CAAC,UAAAL,qBAAA,iBAAxBA,qBAAA,CAA0BM,QAAQ,CAAE,CACxD,CAAC,CACD,EAAAL,sBAAA,CAAA1D,WAAW,CAAC8D,SAAS,CAAC,CAAC,CAAC,UAAAJ,sBAAA,iBAAxBA,sBAAA,CAA0BM,QAAQ,gBACjC5F,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAEf,WAAW,CAAC8D,SAAS,CAAC,CAAC,CAAC,CAACE,QAAS,CAC1C,CACF,CACA,EAAAL,sBAAA,CAAA3D,WAAW,CAAC8D,SAAS,CAAC,CAAC,CAAC,UAAAH,sBAAA,iBAAxBA,sBAAA,CAA0BM,OAAO,gBAChC7F,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAEf,WAAW,CAAC8D,SAAS,CAAC,CAAC,CAAC,CAACG,OAAQ,CACzC,CACF,CACA,EAAAL,sBAAA,CAAA5D,WAAW,CAAC8D,SAAS,CAAC,CAAC,CAAC,UAAAF,sBAAA,iBAAxBA,sBAAA,CAA0BM,UAAU,gBACnC9F,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAEf,WAAW,CAAC8D,SAAS,CAAC,CAAC,CAAC,CAACI,UAAW,CAC5C,CACF,EACD,CACH,cACD9F,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,eAAe,CACrBC,KAAK,CAAErD,UAAU,CAACsC,WAAW,CAACwC,SAAS,CAAE,CAC1C,CAAC,cACFpE,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE9C,oBAAoB,CAAC+B,WAAW,CAAC2B,eAAe,CAAE,CAC1D,CAAC,EACA,CAAC,EACG,CAAC,EACZ,CAED,mBACErD,KAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBjC,IAAA,QAAKgC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C/B,KAAA,CAACzB,IAAI,EACHyD,EAAE,CAAC,eAAe,CAClBF,SAAS,CAAC,yEAAyE,CAAAC,QAAA,eAEnFjC,IAAA,CAACtB,SAAS,EAACsD,SAAS,CAAC,cAAc,CAAE,CAAC,uBAExC,EAAM,CAAC,CACJ,CAAC,cAEN9B,KAAA,QAAK8B,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD/B,KAAA,QAAK8B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjC,IAAA,OAAIgC,SAAS,CAAC,oEAAoE,CAAAC,QAAA,CAAC,qBAEnF,CAAI,CAAC,cACL/B,KAAA,MAAG8B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,kBACxB,CAACL,WAAW,CAACmE,cAAc,EAC1C,CAAC,EACD,CAAC,cACN/F,IAAA,QAAKgC,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxCjC,IAAA,SAAMgC,SAAS,CAAE,SAASxC,cAAc,CAACoC,WAAW,CAACqB,MAAM,CAAErB,WAAW,CAAC,CAACI,SAAS,sBAAuB,CAAAC,QAAA,CACvGzC,cAAc,CAACoC,WAAW,CAACqB,MAAM,CAAErB,WAAW,CAAC,CAACc,KAAK,CAClD,CAAC,CACJ,CAAC,EACH,CAAC,cAGNxC,KAAA,QAAK8B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDjC,IAAA,QAAKgC,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BjC,IAAA,CAACmC,QAAQ,EAACE,KAAK,CAAC,qBAAqB,CAACC,IAAI,CAAE3D,IAAK,CAAAsD,QAAA,cAC/C/B,KAAA,OAAI8B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtCjC,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,MAAM,CACZC,KAAK,CACH,CAAArC,qBAAA,CAAAsB,WAAW,CAACoD,SAAS,UAAA1E,qBAAA,WAArBA,qBAAA,CAAuB0F,OAAO,CAC1BtG,WAAW,CAACkC,WAAW,CAACoD,SAAS,CAACgB,OAAO,CAAC,CAC1C,CAAAzF,mBAAA,CAAAqB,WAAW,CAACqE,MAAM,UAAA1F,mBAAA,WAAlBA,mBAAA,CAAoBoF,QAAQ,CAC1BjG,WAAW,CAACkC,WAAW,CAACqE,MAAM,CAACN,QAAQ,CAAC,CACxC,KACP,CACF,CAAC,cACF3F,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,IAAI,CACVC,KAAK,CACH,EAAAnC,oBAAA,CAAAoB,WAAW,CAACsE,OAAO,UAAA1F,oBAAA,iBAAnBA,oBAAA,CAAqBkE,MAAM,EAAG,CAAC,CAC3B9C,WAAW,CAACsE,OAAO,CAACC,GAAG,CAACC,CAAC,EAAI1G,WAAW,CAAC0G,CAAC,CAACJ,OAAO,CAAC,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,CAC/D,EAAA5F,sBAAA,CAAAmB,WAAW,CAAC8D,SAAS,UAAAjF,sBAAA,iBAArBA,sBAAA,CAAuBiE,MAAM,EAAG,CAAC,CAC/B9C,WAAW,CAAC8D,SAAS,CAACS,GAAG,CAACC,CAAC,EAAI1G,WAAW,CAAC0G,CAAC,CAACT,QAAQ,CAAC,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC,CAClE,eACP,CACF,CAAC,cACFrG,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,SAAS,CACfC,KAAK,CAAEhD,aAAa,EAAAe,oBAAA,CAACkB,WAAW,CAAC0E,OAAO,UAAA5F,oBAAA,iBAAnBA,oBAAA,CAAqB6F,OAAO,CAAE,CACpD,CAAC,cACFvG,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAE,EAAAhC,qBAAA,CAAAiB,WAAW,CAAC0E,OAAO,UAAA3F,qBAAA,iBAAnBA,qBAAA,CAAqB6F,UAAU,GAAI,KAAM,CACjD,CAAC,cACFxG,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,MAAM,CACZC,KAAK,CAAE/C,cAAc,EAAAgB,qBAAA,CAACgB,WAAW,CAAC0E,OAAO,UAAA1F,qBAAA,iBAAnBA,qBAAA,CAAqB6F,IAAI,CAAE,CAClD,CAAC,cACFzG,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAEf,WAAW,CAACmD,IAAI,EAAI,KAAM,CAClC,CAAC,EACA,CAAC,CACG,CAAC,CACR,CAAC,cAEN/E,IAAA,QAAAiC,QAAA,cACEjC,IAAA,CAACmC,QAAQ,EAACE,KAAK,CAAC,oBAAoB,CAACC,IAAI,CAAE1D,KAAM,CAAAqD,QAAA,cAC/C/B,KAAA,OAAI8B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtCjC,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAErD,UAAU,CAACsC,WAAW,CAACwC,SAAS,CAAE,CAC1C,CAAC,cACFpE,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAEpD,kBAAkB,CAACqC,WAAW,CAACwC,SAAS,CAAE,CAClD,CAAC,cACFpE,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAE9C,oBAAoB,CAAC+B,WAAW,CAAC2B,eAAe,CAAE,CAC1D,CAAC,CACD,EAAA1C,sBAAA,CAAAe,WAAW,CAAC4B,UAAU,UAAA3C,sBAAA,iBAAtBA,sBAAA,CAAwBsE,OAAO,gBAC9BnF,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAErD,UAAU,CAACsC,WAAW,CAAC4B,UAAU,CAAC2B,OAAO,CAAE,cAAc,CAAE,CACnE,CACF,CACA,EAAArE,sBAAA,CAAAc,WAAW,CAAC4B,UAAU,UAAA1C,sBAAA,iBAAtBA,sBAAA,CAAwBqD,UAAU,gBACjCnE,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,cAAc,CACpBC,KAAK,CAAErD,UAAU,CAACsC,WAAW,CAAC4B,UAAU,CAACW,UAAU,CAAE,cAAc,CAAE,CACtE,CACF,EACC,CAAC,CACG,CAAC,CACR,CAAC,EACH,CAAC,cAGNjE,KAAA,QAAK8B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDjC,IAAA,CAACmC,QAAQ,EAACE,KAAK,CAAC,wBAAwB,CAACC,IAAI,CAAEzD,MAAO,CAAAoD,QAAA,cACpD/B,KAAA,OAAI8B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtCjC,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,WAAW,CACjBC,KAAK,CAAE,EAAA5B,qBAAA,CAAAa,WAAW,CAAC8E,UAAU,UAAA3F,qBAAA,iBAAtBA,qBAAA,CAAwB4F,SAAS,GAAI,KAAM,CACnD,CAAC,cACF3G,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAE,EAAA3B,sBAAA,CAAAY,WAAW,CAAC8E,UAAU,UAAA1F,sBAAA,iBAAtBA,sBAAA,CAAwB4F,WAAW,GAAI,KAAM,CACrD,CAAC,cACF5G,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,UAAU,CAChBC,KAAK,CAAE,EAAA1B,sBAAA,CAAAW,WAAW,CAAC8E,UAAU,UAAAzF,sBAAA,iBAAtBA,sBAAA,CAAwB4F,QAAQ,GAAI,KAAM,CAClD,CAAC,cACF7G,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAE,EAAAzB,sBAAA,CAAAU,WAAW,CAAC8E,UAAU,UAAAxF,sBAAA,iBAAtBA,sBAAA,CAAwB4F,UAAU,GAAI,KAAM,CACpD,CAAC,cACF9G,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,aAAa,CACnBC,KAAK,CAAE,CAAAxB,sBAAA,CAAAS,WAAW,CAAC8E,UAAU,UAAAvF,sBAAA,YAAAC,sBAAA,CAAtBD,sBAAA,CAAwB4F,GAAG,UAAA3F,sBAAA,WAA3BA,sBAAA,CAA6B4F,OAAO,CAAG,KAAK,CAAG,IAAK,CAC5D,CAAC,CACD,EAAA3F,sBAAA,CAAAO,WAAW,CAAC8E,UAAU,UAAArF,sBAAA,kBAAAC,sBAAA,CAAtBD,sBAAA,CAAwB0F,GAAG,UAAAzF,sBAAA,iBAA3BA,sBAAA,CAA6B2F,MAAM,gBAClCjH,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,YAAY,CAClBC,KAAK,CAAEf,WAAW,CAAC8E,UAAU,CAACK,GAAG,CAACE,MAAO,CAC1C,CACF,EACC,CAAC,CACG,CAAC,cAEXjH,IAAA,CAACmC,QAAQ,EAACE,KAAK,CAAC,gBAAgB,CAACC,IAAI,CAAExD,MAAO,CAAAmD,QAAA,cAC5C/B,KAAA,OAAI8B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACtCjC,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,KAAK,CACXC,KAAK,CAAE,EAAApB,sBAAA,CAAAK,WAAW,CAACsB,cAAc,UAAA3B,sBAAA,iBAA1BA,sBAAA,CAA4B4B,GAAG,GAAI,KAAM,CACjD,CAAC,cACFnD,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,MAAM,CACZC,KAAK,CAAE,EAAAnB,sBAAA,CAAAI,WAAW,CAACsB,cAAc,UAAA1B,sBAAA,iBAA1BA,sBAAA,CAA4B6B,IAAI,GAAI,KAAM,CAClD,CAAC,cACFrD,IAAA,CAACwC,OAAO,EACNE,KAAK,CAAC,OAAO,CACbC,KAAK,CAAE,EAAAlB,uBAAA,CAAAG,WAAW,CAACsB,cAAc,UAAAzB,uBAAA,iBAA1BA,uBAAA,CAA4B6B,KAAK,GAAI,KAAM,CACnD,CAAC,EACA,CAAC,CACG,CAAC,EACR,CAAC,CAGLvB,kBAAkB,GAAK,WAAW,eAAI/B,IAAA,CAAC4C,aAAa,GAAE,CAAC,CACvDb,kBAAkB,GAAK,SAAS,eAAI/B,IAAA,CAAC0D,WAAW,GAAE,CAAC,CACnD3B,kBAAkB,GAAK,UAAU,eAAI/B,IAAA,CAACoF,YAAY,GAAE,CAAC,CACrDrD,kBAAkB,GAAK,QAAQ,eAAI/B,IAAA,CAACqE,UAAU,GAAE,CAAC,CACjDtC,kBAAkB,GAAK,cAAc,eAAI/B,IAAA,CAAC2E,gBAAgB,GAAE,CAAC,CAG7D/C,WAAW,CAACsF,OAAO,EAAIC,MAAM,CAACC,IAAI,CAACxF,WAAW,CAACsF,OAAO,CAAC,CAACxC,MAAM,CAAG,CAAC,eACjE1E,IAAA,CAACmC,QAAQ,EAACE,KAAK,CAAC,eAAe,CAACC,IAAI,CAAEvD,QAAS,CAAAkD,QAAA,cAC7CjC,IAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBkF,MAAM,CAACE,OAAO,CAACzF,WAAW,CAACsF,OAAO,CAAC,CAACf,GAAG,CAACmB,KAAA,MAAC,CAACC,GAAG,CAAE5E,KAAK,CAAC,CAAA2E,KAAA,oBACpDpH,KAAA,QAAe8B,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eACtEjC,IAAA,OAAIgC,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9DsF,GAAG,CAACC,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CACrB,CAAC,cACLxH,IAAA,OAAIgC,SAAS,CAAC,kEAAkE,CAAAC,QAAA,CAC7EnC,iBAAiB,CAAC6C,KAAK,CAAC,CACvB,CAAC,GANG4E,GAOL,CAAC,EACP,CAAC,CACC,CAAC,CACE,CACX,CAGA3F,WAAW,CAAC4B,UAAU,eACrBxD,IAAA,CAACmC,QAAQ,EAACE,KAAK,CAAC,sBAAsB,CAACC,IAAI,CAAEpD,QAAS,CAAA+C,QAAA,cACpDjC,IAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBjC,IAAA,OAAIgC,SAAS,CAAC,OAAO,CAAAC,QAAA,CAClBkF,MAAM,CAACE,OAAO,CAACzF,WAAW,CAAC4B,UAAU,CAAC,CACpCiE,IAAI,CAAC,CAAAC,KAAA,CAAAC,KAAA,OAAC,EAAEC,CAAC,CAAC,CAAAF,KAAA,IAAE,EAAEG,CAAC,CAAC,CAAAF,KAAA,OAAK,IAAI,CAAAG,IAAI,CAACF,CAAC,CAAC,CAAG,GAAI,CAAAE,IAAI,CAACD,CAAC,CAAC,GAAC,CAC/C1B,GAAG,CAAC,CAAA4B,KAAA,CAAqBC,KAAK,CAAEC,KAAK,OAAhC,CAACC,KAAK,CAAE9D,SAAS,CAAC,CAAA2D,KAAA,oBACxB/H,IAAA,OAAAiC,QAAA,cACE/B,KAAA,QAAK8B,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3B+F,KAAK,GAAKC,KAAK,CAACvD,MAAM,CAAG,CAAC,eACzB1E,IAAA,SAAMgC,SAAS,CAAC,uDAAuD,CAAE,CAC1E,cACD9B,KAAA,QAAK8B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCjC,IAAA,QAAAiC,QAAA,cACEjC,IAAA,SAAMgC,SAAS,CAAC,wFAAwF,CAAAC,QAAA,cACtGjC,IAAA,CAACb,IAAI,EAAC6C,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACnC,CAAC,CACJ,CAAC,cACN9B,KAAA,QAAK8B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEjC,IAAA,QAAAiC,QAAA,cACEjC,IAAA,MAAGgC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC5CiG,KAAK,CAACV,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CACxB,CAAC,CACD,CAAC,cACNxH,IAAA,QAAKgC,SAAS,CAAC,oDAAoD,CAAAC,QAAA,cACjEjC,IAAA,SAAMmI,QAAQ,CAAE/D,SAAU,CAAAnC,QAAA,CACvB3C,UAAU,CAAC8E,SAAS,CAAE,cAAc,CAAC,CAClC,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EAxBC8D,KAyBL,CAAC,EACN,CAAC,CACA,CAAC,CACF,CAAC,CACE,CACX,CAGAtG,WAAW,CAAC6C,MAAM,EAAI7C,WAAW,CAAC6C,MAAM,CAACC,MAAM,CAAG,CAAC,eAClD1E,IAAA,CAACmC,QAAQ,EAACE,KAAK,CAAC,QAAQ,CAACC,IAAI,CAAElD,WAAY,CAAA6C,QAAA,cACzCjC,IAAA,QAAKgC,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBL,WAAW,CAAC6C,MAAM,CAAC0B,GAAG,CAAC,CAACrE,KAAK,CAAEkG,KAAK,gBACnChI,IAAA,QAAiBgC,SAAS,CAAC,gDAAgD,CAAAC,QAAA,cACzEjC,IAAA,MAAGgC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAEH,KAAK,CAAI,CAAC,EADvCkG,KAEL,CACN,CAAC,CACC,CAAC,CACE,CACX,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3H,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}