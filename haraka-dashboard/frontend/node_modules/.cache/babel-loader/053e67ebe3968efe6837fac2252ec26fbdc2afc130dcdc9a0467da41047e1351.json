{"ast": null, "code": "import{format,formatDistanceToNow,isValid}from'date-fns';// Format date for display\nexport const formatDate=function(date){let formatString=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'MMM dd, yyyy HH:mm:ss';if(!date)return'N/A';const dateObj=typeof date==='string'?new Date(date):date;if(!isValid(dateObj))return'Invalid Date';return format(dateObj,formatString);};// Format relative time (e.g., \"2 hours ago\")\nexport const formatRelativeTime=date=>{if(!date)return'N/A';const dateObj=typeof date==='string'?new Date(date):date;if(!isValid(dateObj))return'Invalid Date';return formatDistanceToNow(dateObj,{addSuffix:true});};// Format email address for display\nexport const formatEmail=email=>{if(!email)return'N/A';// Remove angle brackets if present\nreturn email.replace(/[<>]/g,'').trim();};// Format subject line\nexport const formatSubject=subject=>{if(!subject)return'No Subject';// Decode basic MIME encoded subjects\nlet decoded=subject;// Handle =?utf-8?q?...?= encoding\nconst mimeRegex=/=\\?([^?]+)\\?([qb])\\?([^?]*)\\?=/gi;decoded=decoded.replace(mimeRegex,(match,charset,encoding,text)=>{if(encoding.toLowerCase()==='q'){// Quoted-printable decoding (basic)\nreturn text.replace(/=([0-9A-F]{2})/gi,(match,hex)=>{return String.fromCharCode(parseInt(hex,16));}).replace(/_/g,' ');}return text;});return decoded.trim();};// Format file size\nexport const formatFileSize=bytes=>{if(!bytes||bytes===0)return'0 B';const k=1024;const sizes=['B','KB','MB','GB'];const i=Math.floor(Math.log(bytes)/Math.log(k));return parseFloat((bytes/Math.pow(k,i)).toFixed(1))+' '+sizes[i];};// Format processing time\nexport const formatProcessingTime=ms=>{if(!ms&&ms!==0)return'N/A';if(ms<1000){return`${ms}ms`;}else{return`${(ms/1000).toFixed(2)}s`;}};// Consolidate intermediate statuses to final display statuses\nexport const consolidateTransactionStatus=transaction=>{var _transaction$authenti,_transaction$authenti2,_transaction$authenti3;if(!transaction)return'unknown';const status=transaction.status;const hasRejectionReason=transaction.rejection_reason;const hasErrors=transaction.errors&&transaction.errors.length>0;const authFailures=transaction.authentication&&(((_transaction$authenti=transaction.authentication.spf)===null||_transaction$authenti===void 0?void 0:_transaction$authenti.result)==='fail'||((_transaction$authenti2=transaction.authentication.dkim)===null||_transaction$authenti2===void 0?void 0:_transaction$authenti2.result)==='fail'||((_transaction$authenti3=transaction.authentication.dmarc)===null||_transaction$authenti3===void 0?void 0:_transaction$authenti3.result)==='fail');// Map statuses to final display states\nswitch(status){case'delivered':case'relayed':case'accepted':case'queued':// Queued emails that completed successfully\nreturn'delivered';case'rejected':case'bounced':// Check if rejection was due to authentication failures\nif(authFailures||hasRejectionReason&&(transaction.rejection_reason.toLowerCase().includes('spf')||transaction.rejection_reason.toLowerCase().includes('dkim')||transaction.rejection_reason.toLowerCase().includes('dmarc')||transaction.rejection_reason.toLowerCase().includes('authentication'))){return'blocked';}return'blocked';// All rejections are considered blocked\ncase'deferred':return'deferred';// Keep deferred as its own status\ncase'failed':return'failed';case'disconnected':return'disconnected';case'in_progress':// If still in progress but has errors, consider it failed\nif(hasErrors)return'failed';return'disconnected';// Assume disconnected if still in progress\ndefault:return'unknown';}};// Format status with appropriate styling using consolidated statuses\nexport const getStatusBadge=function(status){let transaction=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;// If transaction object is provided, use consolidated status\nconst finalStatus=transaction?consolidateTransactionStatus(transaction):status;const statusMap={'delivered':{label:'Delivered',className:'badge-success'},'blocked':{label:'Blocked',className:'badge-danger'},'deferred':{label:'Deferred',className:'badge-warning'},'failed':{label:'Failed',className:'badge-danger'},'disconnected':{label:'Disconnected',className:'badge-warning'},'unknown':{label:'Unknown',className:'badge-info'},// Legacy statuses for backward compatibility\n'relayed':{label:'Delivered',className:'badge-success'},'queued':{label:'Delivered',className:'badge-success'},'rejected':{label:'Blocked',className:'badge-danger'},'bounced':{label:'Blocked',className:'badge-danger'},'accepted':{label:'Delivered',className:'badge-success'},'in_progress':{label:'Disconnected',className:'badge-warning'}};return statusMap[finalStatus]||{label:finalStatus||'Unknown',className:'badge-info'};};// Format IP address\nexport const formatIP=ip=>{if(!ip)return'N/A';return ip;};// Format hostname\nexport const formatHostname=hostname=>{if(!hostname)return'N/A';return hostname;};// Truncate text with ellipsis\nexport const truncateText=function(text){let maxLength=arguments.length>1&&arguments[1]!==undefined?arguments[1]:50;if(!text)return'';if(text.length<=maxLength)return text;return text.substring(0,maxLength)+'...';};// Format header value for display\nexport const formatHeaderValue=value=>{if(!value)return'N/A';if(Array.isArray(value)){return value.join(', ').trim();}return String(value).trim();};// Format defer reason (handle object or string)\nexport const formatDeferReason=deferReason=>{if(!deferReason)return'No defer reason specified';// If it's already a string, return it\nif(typeof deferReason==='string'){return deferReason==='[object Object]'?'Defer reason not properly logged':deferReason;}// If it's an object, try to extract meaningful information\nif(typeof deferReason==='object'){// Common patterns in Haraka defer reasons\nif(deferReason.message)return deferReason.message;if(deferReason.error)return deferReason.error;if(deferReason.reason)return deferReason.reason;if(deferReason.code&&deferReason.msg)return`${deferReason.code}: ${deferReason.msg}`;if(deferReason.toString&&typeof deferReason.toString==='function'){const stringified=deferReason.toString();return stringified!=='[object Object]'?stringified:'Defer reason object could not be parsed';}// Try to extract any meaningful string values from the object\nconst values=Object.values(deferReason).filter(v=>typeof v==='string'&&v.length>0&&v!=='[object Object]');if(values.length>0){return values.join('; ');}return'Defer reason object could not be parsed';}return String(deferReason);};// Extract domain from email\nexport const extractDomain=email=>{if(!email)return'N/A';const cleaned=formatEmail(email);const atIndex=cleaned.lastIndexOf('@');if(atIndex===-1)return'N/A';return cleaned.substring(atIndex+1);};// Get recipients from transaction (handles multiple data sources)\nexport const getTransactionRecipients=transaction=>{// Try rcpt_to first (standard SMTP transactions)\nif(transaction.rcpt_to&&transaction.rcpt_to.length>0){return transaction.rcpt_to.map(r=>r.address||r.original).filter(Boolean);}// Try recipient field (delivery status transactions)\nif(transaction.recipient&&transaction.recipient.length>0){return transaction.recipient.map(r=>r.original||r.address).filter(Boolean);}// Try headers.to (relayed emails)\nif(transaction.headers&&transaction.headers.to){const toHeaders=Array.isArray(transaction.headers.to)?transaction.headers.to:[transaction.headers.to];const recipients=[];toHeaders.forEach(header=>{// Extract email addresses from header like \"\\\"Moshiko\\\" <<EMAIL>>\"\nconst emailMatches=header.match(/<([^>]+)>/g);if(emailMatches){emailMatches.forEach(match=>{const email=match.replace(/[<>]/g,'');if(email)recipients.push(email);});}else{// Simple email without angle brackets\nconst simpleEmail=header.trim().replace(/\\n$/,'');if(simpleEmail&&simpleEmail.includes('@')){recipients.push(simpleEmail);}}});return recipients;}return[];};// Format recipient list\nexport const formatRecipients=recipients=>{if(!recipients||!Array.isArray(recipients))return'N/A';if(recipients.length===0)return'No recipients';if(recipients.length===1){return formatEmail(recipients[0].address||recipients[0]);}const first=formatEmail(recipients[0].address||recipients[0]);return`${first} (+${recipients.length-1} more)`;};", "map": {"version": 3, "names": ["format", "formatDistanceToNow", "<PERSON><PERSON><PERSON><PERSON>", "formatDate", "date", "formatString", "arguments", "length", "undefined", "date<PERSON><PERSON>j", "Date", "formatRelativeTime", "addSuffix", "formatEmail", "email", "replace", "trim", "formatSubject", "subject", "decoded", "mimeRegex", "match", "charset", "encoding", "text", "toLowerCase", "hex", "String", "fromCharCode", "parseInt", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "formatProcessingTime", "ms", "consolidateTransactionStatus", "transaction", "_transaction$authenti", "_transaction$authenti2", "_transaction$authenti3", "status", "hasRejectionReason", "rejection_reason", "hasErrors", "errors", "authFailures", "authentication", "spf", "result", "dkim", "dmarc", "includes", "getStatusBadge", "finalStatus", "statusMap", "label", "className", "formatIP", "ip", "formatHostname", "hostname", "truncateText", "max<PERSON><PERSON><PERSON>", "substring", "formatHeaderValue", "value", "Array", "isArray", "join", "formatDeferReason", "deferReason", "message", "error", "reason", "code", "msg", "toString", "stringified", "values", "Object", "filter", "v", "extractDomain", "cleaned", "atIndex", "lastIndexOf", "getTransactionRecipients", "rcpt_to", "map", "r", "address", "original", "Boolean", "recipient", "headers", "to", "toHeaders", "recipients", "for<PERSON>ach", "header", "emailMatches", "push", "simpleEmail", "formatRecipients", "first"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/utils/formatters.js"], "sourcesContent": ["import { format, formatDistanceToNow, isValid } from 'date-fns';\n\n// Format date for display\nexport const formatDate = (date, formatString = 'MMM dd, yyyy HH:mm:ss') => {\n  if (!date) return 'N/A';\n  \n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  if (!isValid(dateObj)) return 'Invalid Date';\n  \n  return format(dateObj, formatString);\n};\n\n// Format relative time (e.g., \"2 hours ago\")\nexport const formatRelativeTime = (date) => {\n  if (!date) return 'N/A';\n  \n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  if (!isValid(dateObj)) return 'Invalid Date';\n  \n  return formatDistanceToNow(dateObj, { addSuffix: true });\n};\n\n// Format email address for display\nexport const formatEmail = (email) => {\n  if (!email) return 'N/A';\n  \n  // Remove angle brackets if present\n  return email.replace(/[<>]/g, '').trim();\n};\n\n// Format subject line\nexport const formatSubject = (subject) => {\n  if (!subject) return 'No Subject';\n  \n  // Decode basic MIME encoded subjects\n  let decoded = subject;\n  \n  // Handle =?utf-8?q?...?= encoding\n  const mimeRegex = /=\\?([^?]+)\\?([qb])\\?([^?]*)\\?=/gi;\n  decoded = decoded.replace(mimeRegex, (match, charset, encoding, text) => {\n    if (encoding.toLowerCase() === 'q') {\n      // Quoted-printable decoding (basic)\n      return text.replace(/=([0-9A-F]{2})/gi, (match, hex) => {\n        return String.fromCharCode(parseInt(hex, 16));\n      }).replace(/_/g, ' ');\n    }\n    return text;\n  });\n  \n  return decoded.trim();\n};\n\n// Format file size\nexport const formatFileSize = (bytes) => {\n  if (!bytes || bytes === 0) return '0 B';\n  \n  const k = 1024;\n  const sizes = ['B', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\n};\n\n// Format processing time\nexport const formatProcessingTime = (ms) => {\n  if (!ms && ms !== 0) return 'N/A';\n  \n  if (ms < 1000) {\n    return `${ms}ms`;\n  } else {\n    return `${(ms / 1000).toFixed(2)}s`;\n  }\n};\n\n// Consolidate intermediate statuses to final display statuses\nexport const consolidateTransactionStatus = (transaction) => {\n  if (!transaction) return 'unknown';\n\n  const status = transaction.status;\n  const hasRejectionReason = transaction.rejection_reason;\n  const hasErrors = transaction.errors && transaction.errors.length > 0;\n  const authFailures = transaction.authentication && (\n    transaction.authentication.spf?.result === 'fail' ||\n    transaction.authentication.dkim?.result === 'fail' ||\n    transaction.authentication.dmarc?.result === 'fail'\n  );\n\n  // Map statuses to final display states\n  switch (status) {\n    case 'delivered':\n    case 'relayed':\n    case 'accepted':\n    case 'queued': // Queued emails that completed successfully\n      return 'delivered';\n\n    case 'rejected':\n    case 'bounced':\n      // Check if rejection was due to authentication failures\n      if (authFailures || (hasRejectionReason &&\n          (transaction.rejection_reason.toLowerCase().includes('spf') ||\n           transaction.rejection_reason.toLowerCase().includes('dkim') ||\n           transaction.rejection_reason.toLowerCase().includes('dmarc') ||\n           transaction.rejection_reason.toLowerCase().includes('authentication')))) {\n        return 'blocked';\n      }\n      return 'blocked'; // All rejections are considered blocked\n\n    case 'deferred':\n      return 'deferred'; // Keep deferred as its own status\n\n    case 'failed':\n      return 'failed';\n\n    case 'disconnected':\n      return 'disconnected';\n\n    case 'in_progress':\n      // If still in progress but has errors, consider it failed\n      if (hasErrors) return 'failed';\n      return 'disconnected'; // Assume disconnected if still in progress\n\n    default:\n      return 'unknown';\n  }\n};\n\n// Format status with appropriate styling using consolidated statuses\nexport const getStatusBadge = (status, transaction = null) => {\n  // If transaction object is provided, use consolidated status\n  const finalStatus = transaction ? consolidateTransactionStatus(transaction) : status;\n\n  const statusMap = {\n    'delivered': { label: 'Delivered', className: 'badge-success' },\n    'blocked': { label: 'Blocked', className: 'badge-danger' },\n    'deferred': { label: 'Deferred', className: 'badge-warning' },\n    'failed': { label: 'Failed', className: 'badge-danger' },\n    'disconnected': { label: 'Disconnected', className: 'badge-warning' },\n    'unknown': { label: 'Unknown', className: 'badge-info' },\n\n    // Legacy statuses for backward compatibility\n    'relayed': { label: 'Delivered', className: 'badge-success' },\n    'queued': { label: 'Delivered', className: 'badge-success' },\n    'rejected': { label: 'Blocked', className: 'badge-danger' },\n    'bounced': { label: 'Blocked', className: 'badge-danger' },\n    'accepted': { label: 'Delivered', className: 'badge-success' },\n    'in_progress': { label: 'Disconnected', className: 'badge-warning' },\n  };\n\n  return statusMap[finalStatus] || { label: finalStatus || 'Unknown', className: 'badge-info' };\n};\n\n// Format IP address\nexport const formatIP = (ip) => {\n  if (!ip) return 'N/A';\n  return ip;\n};\n\n// Format hostname\nexport const formatHostname = (hostname) => {\n  if (!hostname) return 'N/A';\n  return hostname;\n};\n\n// Truncate text with ellipsis\nexport const truncateText = (text, maxLength = 50) => {\n  if (!text) return '';\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n};\n\n// Format header value for display\nexport const formatHeaderValue = (value) => {\n  if (!value) return 'N/A';\n\n  if (Array.isArray(value)) {\n    return value.join(', ').trim();\n  }\n\n  return String(value).trim();\n};\n\n// Format defer reason (handle object or string)\nexport const formatDeferReason = (deferReason) => {\n  if (!deferReason) return 'No defer reason specified';\n\n  // If it's already a string, return it\n  if (typeof deferReason === 'string') {\n    return deferReason === '[object Object]' ? 'Defer reason not properly logged' : deferReason;\n  }\n\n  // If it's an object, try to extract meaningful information\n  if (typeof deferReason === 'object') {\n    // Common patterns in Haraka defer reasons\n    if (deferReason.message) return deferReason.message;\n    if (deferReason.error) return deferReason.error;\n    if (deferReason.reason) return deferReason.reason;\n    if (deferReason.code && deferReason.msg) return `${deferReason.code}: ${deferReason.msg}`;\n    if (deferReason.toString && typeof deferReason.toString === 'function') {\n      const stringified = deferReason.toString();\n      return stringified !== '[object Object]' ? stringified : 'Defer reason object could not be parsed';\n    }\n\n    // Try to extract any meaningful string values from the object\n    const values = Object.values(deferReason).filter(v =>\n      typeof v === 'string' && v.length > 0 && v !== '[object Object]'\n    );\n\n    if (values.length > 0) {\n      return values.join('; ');\n    }\n\n    return 'Defer reason object could not be parsed';\n  }\n\n  return String(deferReason);\n};\n\n// Extract domain from email\nexport const extractDomain = (email) => {\n  if (!email) return 'N/A';\n  \n  const cleaned = formatEmail(email);\n  const atIndex = cleaned.lastIndexOf('@');\n  \n  if (atIndex === -1) return 'N/A';\n  \n  return cleaned.substring(atIndex + 1);\n};\n\n// Get recipients from transaction (handles multiple data sources)\nexport const getTransactionRecipients = (transaction) => {\n  // Try rcpt_to first (standard SMTP transactions)\n  if (transaction.rcpt_to && transaction.rcpt_to.length > 0) {\n    return transaction.rcpt_to.map(r => r.address || r.original).filter(Boolean);\n  }\n\n  // Try recipient field (delivery status transactions)\n  if (transaction.recipient && transaction.recipient.length > 0) {\n    return transaction.recipient.map(r => r.original || r.address).filter(Boolean);\n  }\n\n  // Try headers.to (relayed emails)\n  if (transaction.headers && transaction.headers.to) {\n    const toHeaders = Array.isArray(transaction.headers.to) ? transaction.headers.to : [transaction.headers.to];\n    const recipients = [];\n\n    toHeaders.forEach(header => {\n      // Extract email addresses from header like \"\\\"Moshiko\\\" <<EMAIL>>\"\n      const emailMatches = header.match(/<([^>]+)>/g);\n      if (emailMatches) {\n        emailMatches.forEach(match => {\n          const email = match.replace(/[<>]/g, '');\n          if (email) recipients.push(email);\n        });\n      } else {\n        // Simple email without angle brackets\n        const simpleEmail = header.trim().replace(/\\n$/, '');\n        if (simpleEmail && simpleEmail.includes('@')) {\n          recipients.push(simpleEmail);\n        }\n      }\n    });\n\n    return recipients;\n  }\n\n  return [];\n};\n\n// Format recipient list\nexport const formatRecipients = (recipients) => {\n  if (!recipients || !Array.isArray(recipients)) return 'N/A';\n\n  if (recipients.length === 0) return 'No recipients';\n\n  if (recipients.length === 1) {\n    return formatEmail(recipients[0].address || recipients[0]);\n  }\n\n  const first = formatEmail(recipients[0].address || recipients[0]);\n  return `${first} (+${recipients.length - 1} more)`;\n};\n"], "mappings": "AAAA,OAASA,MAAM,CAAEC,mBAAmB,CAAEC,OAAO,KAAQ,UAAU,CAE/D;AACA,MAAO,MAAM,CAAAC,UAAU,CAAG,QAAAA,CAACC,IAAI,CAA6C,IAA3C,CAAAC,YAAY,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,uBAAuB,CACrE,GAAI,CAACF,IAAI,CAAE,MAAO,KAAK,CAEvB,KAAM,CAAAK,OAAO,CAAG,MAAO,CAAAL,IAAI,GAAK,QAAQ,CAAG,GAAI,CAAAM,IAAI,CAACN,IAAI,CAAC,CAAGA,IAAI,CAEhE,GAAI,CAACF,OAAO,CAACO,OAAO,CAAC,CAAE,MAAO,cAAc,CAE5C,MAAO,CAAAT,MAAM,CAACS,OAAO,CAAEJ,YAAY,CAAC,CACtC,CAAC,CAED;AACA,MAAO,MAAM,CAAAM,kBAAkB,CAAIP,IAAI,EAAK,CAC1C,GAAI,CAACA,IAAI,CAAE,MAAO,KAAK,CAEvB,KAAM,CAAAK,OAAO,CAAG,MAAO,CAAAL,IAAI,GAAK,QAAQ,CAAG,GAAI,CAAAM,IAAI,CAACN,IAAI,CAAC,CAAGA,IAAI,CAEhE,GAAI,CAACF,OAAO,CAACO,OAAO,CAAC,CAAE,MAAO,cAAc,CAE5C,MAAO,CAAAR,mBAAmB,CAACQ,OAAO,CAAE,CAAEG,SAAS,CAAE,IAAK,CAAC,CAAC,CAC1D,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,WAAW,CAAIC,KAAK,EAAK,CACpC,GAAI,CAACA,KAAK,CAAE,MAAO,KAAK,CAExB;AACA,MAAO,CAAAA,KAAK,CAACC,OAAO,CAAC,OAAO,CAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAC1C,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,aAAa,CAAIC,OAAO,EAAK,CACxC,GAAI,CAACA,OAAO,CAAE,MAAO,YAAY,CAEjC;AACA,GAAI,CAAAC,OAAO,CAAGD,OAAO,CAErB;AACA,KAAM,CAAAE,SAAS,CAAG,kCAAkC,CACpDD,OAAO,CAAGA,OAAO,CAACJ,OAAO,CAACK,SAAS,CAAE,CAACC,KAAK,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,IAAI,GAAK,CACvE,GAAID,QAAQ,CAACE,WAAW,CAAC,CAAC,GAAK,GAAG,CAAE,CAClC;AACA,MAAO,CAAAD,IAAI,CAACT,OAAO,CAAC,kBAAkB,CAAE,CAACM,KAAK,CAAEK,GAAG,GAAK,CACtD,MAAO,CAAAC,MAAM,CAACC,YAAY,CAACC,QAAQ,CAACH,GAAG,CAAE,EAAE,CAAC,CAAC,CAC/C,CAAC,CAAC,CAACX,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CACvB,CACA,MAAO,CAAAS,IAAI,CACb,CAAC,CAAC,CAEF,MAAO,CAAAL,OAAO,CAACH,IAAI,CAAC,CAAC,CACvB,CAAC,CAED;AACA,MAAO,MAAM,CAAAc,cAAc,CAAIC,KAAK,EAAK,CACvC,GAAI,CAACA,KAAK,EAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,KAAK,CAEvC,KAAM,CAAAC,CAAC,CAAG,IAAI,CACd,KAAM,CAAAC,KAAK,CAAG,CAAC,GAAG,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACrC,KAAM,CAAAC,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,CAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC,CAEnD,MAAO,CAAAM,UAAU,CAAC,CAACP,KAAK,CAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,CAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAG,GAAG,CAAGP,KAAK,CAACC,CAAC,CAAC,CACzE,CAAC,CAED;AACA,MAAO,MAAM,CAAAO,oBAAoB,CAAIC,EAAE,EAAK,CAC1C,GAAI,CAACA,EAAE,EAAIA,EAAE,GAAK,CAAC,CAAE,MAAO,KAAK,CAEjC,GAAIA,EAAE,CAAG,IAAI,CAAE,CACb,MAAO,GAAGA,EAAE,IAAI,CAClB,CAAC,IAAM,CACL,MAAO,GAAG,CAACA,EAAE,CAAG,IAAI,EAAEF,OAAO,CAAC,CAAC,CAAC,GAAG,CACrC,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAG,4BAA4B,CAAIC,WAAW,EAAK,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC3D,GAAI,CAACH,WAAW,CAAE,MAAO,SAAS,CAElC,KAAM,CAAAI,MAAM,CAAGJ,WAAW,CAACI,MAAM,CACjC,KAAM,CAAAC,kBAAkB,CAAGL,WAAW,CAACM,gBAAgB,CACvD,KAAM,CAAAC,SAAS,CAAGP,WAAW,CAACQ,MAAM,EAAIR,WAAW,CAACQ,MAAM,CAAC7C,MAAM,CAAG,CAAC,CACrE,KAAM,CAAA8C,YAAY,CAAGT,WAAW,CAACU,cAAc,GAC7C,EAAAT,qBAAA,CAAAD,WAAW,CAACU,cAAc,CAACC,GAAG,UAAAV,qBAAA,iBAA9BA,qBAAA,CAAgCW,MAAM,IAAK,MAAM,EACjD,EAAAV,sBAAA,CAAAF,WAAW,CAACU,cAAc,CAACG,IAAI,UAAAX,sBAAA,iBAA/BA,sBAAA,CAAiCU,MAAM,IAAK,MAAM,EAClD,EAAAT,sBAAA,CAAAH,WAAW,CAACU,cAAc,CAACI,KAAK,UAAAX,sBAAA,iBAAhCA,sBAAA,CAAkCS,MAAM,IAAK,MAAM,CACpD,CAED;AACA,OAAQR,MAAM,EACZ,IAAK,WAAW,CAChB,IAAK,SAAS,CACd,IAAK,UAAU,CACf,IAAK,QAAQ,CAAE;AACb,MAAO,WAAW,CAEpB,IAAK,UAAU,CACf,IAAK,SAAS,CACZ;AACA,GAAIK,YAAY,EAAKJ,kBAAkB,GAClCL,WAAW,CAACM,gBAAgB,CAACzB,WAAW,CAAC,CAAC,CAACkC,QAAQ,CAAC,KAAK,CAAC,EAC1Df,WAAW,CAACM,gBAAgB,CAACzB,WAAW,CAAC,CAAC,CAACkC,QAAQ,CAAC,MAAM,CAAC,EAC3Df,WAAW,CAACM,gBAAgB,CAACzB,WAAW,CAAC,CAAC,CAACkC,QAAQ,CAAC,OAAO,CAAC,EAC5Df,WAAW,CAACM,gBAAgB,CAACzB,WAAW,CAAC,CAAC,CAACkC,QAAQ,CAAC,gBAAgB,CAAC,CAAE,CAAE,CAC5E,MAAO,SAAS,CAClB,CACA,MAAO,SAAS,CAAE;AAEpB,IAAK,UAAU,CACb,MAAO,UAAU,CAAE;AAErB,IAAK,QAAQ,CACX,MAAO,QAAQ,CAEjB,IAAK,cAAc,CACjB,MAAO,cAAc,CAEvB,IAAK,aAAa,CAChB;AACA,GAAIR,SAAS,CAAE,MAAO,QAAQ,CAC9B,MAAO,cAAc,CAAE;AAEzB,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAS,cAAc,CAAG,QAAAA,CAACZ,MAAM,CAAyB,IAAvB,CAAAJ,WAAW,CAAAtC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACvD;AACA,KAAM,CAAAuD,WAAW,CAAGjB,WAAW,CAAGD,4BAA4B,CAACC,WAAW,CAAC,CAAGI,MAAM,CAEpF,KAAM,CAAAc,SAAS,CAAG,CAChB,WAAW,CAAE,CAAEC,KAAK,CAAE,WAAW,CAAEC,SAAS,CAAE,eAAgB,CAAC,CAC/D,SAAS,CAAE,CAAED,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,cAAe,CAAC,CAC1D,UAAU,CAAE,CAAED,KAAK,CAAE,UAAU,CAAEC,SAAS,CAAE,eAAgB,CAAC,CAC7D,QAAQ,CAAE,CAAED,KAAK,CAAE,QAAQ,CAAEC,SAAS,CAAE,cAAe,CAAC,CACxD,cAAc,CAAE,CAAED,KAAK,CAAE,cAAc,CAAEC,SAAS,CAAE,eAAgB,CAAC,CACrE,SAAS,CAAE,CAAED,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,YAAa,CAAC,CAExD;AACA,SAAS,CAAE,CAAED,KAAK,CAAE,WAAW,CAAEC,SAAS,CAAE,eAAgB,CAAC,CAC7D,QAAQ,CAAE,CAAED,KAAK,CAAE,WAAW,CAAEC,SAAS,CAAE,eAAgB,CAAC,CAC5D,UAAU,CAAE,CAAED,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,cAAe,CAAC,CAC3D,SAAS,CAAE,CAAED,KAAK,CAAE,SAAS,CAAEC,SAAS,CAAE,cAAe,CAAC,CAC1D,UAAU,CAAE,CAAED,KAAK,CAAE,WAAW,CAAEC,SAAS,CAAE,eAAgB,CAAC,CAC9D,aAAa,CAAE,CAAED,KAAK,CAAE,cAAc,CAAEC,SAAS,CAAE,eAAgB,CACrE,CAAC,CAED,MAAO,CAAAF,SAAS,CAACD,WAAW,CAAC,EAAI,CAAEE,KAAK,CAAEF,WAAW,EAAI,SAAS,CAAEG,SAAS,CAAE,YAAa,CAAC,CAC/F,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,QAAQ,CAAIC,EAAE,EAAK,CAC9B,GAAI,CAACA,EAAE,CAAE,MAAO,KAAK,CACrB,MAAO,CAAAA,EAAE,CACX,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,cAAc,CAAIC,QAAQ,EAAK,CAC1C,GAAI,CAACA,QAAQ,CAAE,MAAO,KAAK,CAC3B,MAAO,CAAAA,QAAQ,CACjB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,YAAY,CAAG,QAAAA,CAAC7C,IAAI,CAAqB,IAAnB,CAAA8C,SAAS,CAAAhE,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC/C,GAAI,CAACkB,IAAI,CAAE,MAAO,EAAE,CACpB,GAAIA,IAAI,CAACjB,MAAM,EAAI+D,SAAS,CAAE,MAAO,CAAA9C,IAAI,CACzC,MAAO,CAAAA,IAAI,CAAC+C,SAAS,CAAC,CAAC,CAAED,SAAS,CAAC,CAAG,KAAK,CAC7C,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,iBAAiB,CAAIC,KAAK,EAAK,CAC1C,GAAI,CAACA,KAAK,CAAE,MAAO,KAAK,CAExB,GAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,CAAE,CACxB,MAAO,CAAAA,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC5D,IAAI,CAAC,CAAC,CAChC,CAEA,MAAO,CAAAW,MAAM,CAAC8C,KAAK,CAAC,CAACzD,IAAI,CAAC,CAAC,CAC7B,CAAC,CAED;AACA,MAAO,MAAM,CAAA6D,iBAAiB,CAAIC,WAAW,EAAK,CAChD,GAAI,CAACA,WAAW,CAAE,MAAO,2BAA2B,CAEpD;AACA,GAAI,MAAO,CAAAA,WAAW,GAAK,QAAQ,CAAE,CACnC,MAAO,CAAAA,WAAW,GAAK,iBAAiB,CAAG,kCAAkC,CAAGA,WAAW,CAC7F,CAEA;AACA,GAAI,MAAO,CAAAA,WAAW,GAAK,QAAQ,CAAE,CACnC;AACA,GAAIA,WAAW,CAACC,OAAO,CAAE,MAAO,CAAAD,WAAW,CAACC,OAAO,CACnD,GAAID,WAAW,CAACE,KAAK,CAAE,MAAO,CAAAF,WAAW,CAACE,KAAK,CAC/C,GAAIF,WAAW,CAACG,MAAM,CAAE,MAAO,CAAAH,WAAW,CAACG,MAAM,CACjD,GAAIH,WAAW,CAACI,IAAI,EAAIJ,WAAW,CAACK,GAAG,CAAE,MAAO,GAAGL,WAAW,CAACI,IAAI,KAAKJ,WAAW,CAACK,GAAG,EAAE,CACzF,GAAIL,WAAW,CAACM,QAAQ,EAAI,MAAO,CAAAN,WAAW,CAACM,QAAQ,GAAK,UAAU,CAAE,CACtE,KAAM,CAAAC,WAAW,CAAGP,WAAW,CAACM,QAAQ,CAAC,CAAC,CAC1C,MAAO,CAAAC,WAAW,GAAK,iBAAiB,CAAGA,WAAW,CAAG,yCAAyC,CACpG,CAEA;AACA,KAAM,CAAAC,MAAM,CAAGC,MAAM,CAACD,MAAM,CAACR,WAAW,CAAC,CAACU,MAAM,CAACC,CAAC,EAChD,MAAO,CAAAA,CAAC,GAAK,QAAQ,EAAIA,CAAC,CAAClF,MAAM,CAAG,CAAC,EAAIkF,CAAC,GAAK,iBACjD,CAAC,CAED,GAAIH,MAAM,CAAC/E,MAAM,CAAG,CAAC,CAAE,CACrB,MAAO,CAAA+E,MAAM,CAACV,IAAI,CAAC,IAAI,CAAC,CAC1B,CAEA,MAAO,yCAAyC,CAClD,CAEA,MAAO,CAAAjD,MAAM,CAACmD,WAAW,CAAC,CAC5B,CAAC,CAED;AACA,MAAO,MAAM,CAAAY,aAAa,CAAI5E,KAAK,EAAK,CACtC,GAAI,CAACA,KAAK,CAAE,MAAO,KAAK,CAExB,KAAM,CAAA6E,OAAO,CAAG9E,WAAW,CAACC,KAAK,CAAC,CAClC,KAAM,CAAA8E,OAAO,CAAGD,OAAO,CAACE,WAAW,CAAC,GAAG,CAAC,CAExC,GAAID,OAAO,GAAK,CAAC,CAAC,CAAE,MAAO,KAAK,CAEhC,MAAO,CAAAD,OAAO,CAACpB,SAAS,CAACqB,OAAO,CAAG,CAAC,CAAC,CACvC,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,wBAAwB,CAAIlD,WAAW,EAAK,CACvD;AACA,GAAIA,WAAW,CAACmD,OAAO,EAAInD,WAAW,CAACmD,OAAO,CAACxF,MAAM,CAAG,CAAC,CAAE,CACzD,MAAO,CAAAqC,WAAW,CAACmD,OAAO,CAACC,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,OAAO,EAAID,CAAC,CAACE,QAAQ,CAAC,CAACX,MAAM,CAACY,OAAO,CAAC,CAC9E,CAEA;AACA,GAAIxD,WAAW,CAACyD,SAAS,EAAIzD,WAAW,CAACyD,SAAS,CAAC9F,MAAM,CAAG,CAAC,CAAE,CAC7D,MAAO,CAAAqC,WAAW,CAACyD,SAAS,CAACL,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACE,QAAQ,EAAIF,CAAC,CAACC,OAAO,CAAC,CAACV,MAAM,CAACY,OAAO,CAAC,CAChF,CAEA;AACA,GAAIxD,WAAW,CAAC0D,OAAO,EAAI1D,WAAW,CAAC0D,OAAO,CAACC,EAAE,CAAE,CACjD,KAAM,CAAAC,SAAS,CAAG9B,KAAK,CAACC,OAAO,CAAC/B,WAAW,CAAC0D,OAAO,CAACC,EAAE,CAAC,CAAG3D,WAAW,CAAC0D,OAAO,CAACC,EAAE,CAAG,CAAC3D,WAAW,CAAC0D,OAAO,CAACC,EAAE,CAAC,CAC3G,KAAM,CAAAE,UAAU,CAAG,EAAE,CAErBD,SAAS,CAACE,OAAO,CAACC,MAAM,EAAI,CAC1B;AACA,KAAM,CAAAC,YAAY,CAAGD,MAAM,CAACtF,KAAK,CAAC,YAAY,CAAC,CAC/C,GAAIuF,YAAY,CAAE,CAChBA,YAAY,CAACF,OAAO,CAACrF,KAAK,EAAI,CAC5B,KAAM,CAAAP,KAAK,CAAGO,KAAK,CAACN,OAAO,CAAC,OAAO,CAAE,EAAE,CAAC,CACxC,GAAID,KAAK,CAAE2F,UAAU,CAACI,IAAI,CAAC/F,KAAK,CAAC,CACnC,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA,KAAM,CAAAgG,WAAW,CAAGH,MAAM,CAAC3F,IAAI,CAAC,CAAC,CAACD,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CACpD,GAAI+F,WAAW,EAAIA,WAAW,CAACnD,QAAQ,CAAC,GAAG,CAAC,CAAE,CAC5C8C,UAAU,CAACI,IAAI,CAACC,WAAW,CAAC,CAC9B,CACF,CACF,CAAC,CAAC,CAEF,MAAO,CAAAL,UAAU,CACnB,CAEA,MAAO,EAAE,CACX,CAAC,CAED;AACA,MAAO,MAAM,CAAAM,gBAAgB,CAAIN,UAAU,EAAK,CAC9C,GAAI,CAACA,UAAU,EAAI,CAAC/B,KAAK,CAACC,OAAO,CAAC8B,UAAU,CAAC,CAAE,MAAO,KAAK,CAE3D,GAAIA,UAAU,CAAClG,MAAM,GAAK,CAAC,CAAE,MAAO,eAAe,CAEnD,GAAIkG,UAAU,CAAClG,MAAM,GAAK,CAAC,CAAE,CAC3B,MAAO,CAAAM,WAAW,CAAC4F,UAAU,CAAC,CAAC,CAAC,CAACP,OAAO,EAAIO,UAAU,CAAC,CAAC,CAAC,CAAC,CAC5D,CAEA,KAAM,CAAAO,KAAK,CAAGnG,WAAW,CAAC4F,UAAU,CAAC,CAAC,CAAC,CAACP,OAAO,EAAIO,UAAU,CAAC,CAAC,CAAC,CAAC,CACjE,MAAO,GAAGO,KAAK,MAAMP,UAAU,CAAClG,MAAM,CAAG,CAAC,QAAQ,CACpD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}