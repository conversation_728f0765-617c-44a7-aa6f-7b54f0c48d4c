{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Link}from'react-router-dom';import{Search,Filter,ChevronLeft,ChevronRight,Mail,Calendar,Download,RefreshCw,Eye,ArrowUpDown,ArrowUp,ArrowDown}from'lucide-react';import{useTransactions}from'../hooks/useApi';import{formatDate,formatRelativeTime,getStatusBadge,consolidateTransactionStatus,formatEmail,formatSubject,formatFileSize,formatProcessingTime,truncateText}from'../utils/formatters';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Transactions=()=>{var _data$transactions;const[filters,setFilters]=useState({page:1,limit:25,sortBy:'timestamp',sortOrder:'desc',search:'',status:'',dateFrom:'',dateTo:''});const[searchInput,setSearchInput]=useState('');const[showFilters,setShowFilters]=useState(false);const{data,loading,error,refetch}=useTransactions();// Fetch data when filters change\nuseEffect(()=>{refetch(filters);},[filters]);// Handle search\nconst handleSearch=e=>{e.preventDefault();setFilters(prev=>({...prev,search:searchInput,page:1}));};// Handle filter changes\nconst handleFilterChange=(key,value)=>{setFilters(prev=>({...prev,[key]:value,page:1}));};// Handle sorting\nconst handleSort=column=>{setFilters(prev=>({...prev,sortBy:column,sortOrder:prev.sortBy===column&&prev.sortOrder==='desc'?'asc':'desc',page:1}));};// Handle pagination\nconst handlePageChange=newPage=>{setFilters(prev=>({...prev,page:newPage}));};// Clear filters\nconst clearFilters=()=>{setFilters({page:1,limit:25,sortBy:'timestamp',sortOrder:'desc',search:'',status:'',dateFrom:'',dateTo:''});setSearchInput('');};// Get sort icon\nconst getSortIcon=column=>{if(filters.sortBy!==column){return/*#__PURE__*/_jsx(ArrowUpDown,{className:\"h-4 w-4\"});}return filters.sortOrder==='desc'?/*#__PURE__*/_jsx(ArrowDown,{className:\"h-4 w-4\"}):/*#__PURE__*/_jsx(ArrowUp,{className:\"h-4 w-4\"});};const SortableHeader=_ref=>{let{column,children,className=''}=_ref;return/*#__PURE__*/_jsx(\"th\",{className:`table-header-cell ${className}`,onClick:()=>handleSort(column),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(\"span\",{children:children}),getSortIcon(column)]})});};if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(Mail,{className:\"mx-auto h-12 w-12 text-red-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"Error loading transactions\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:error}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>refetch(filters),className:\"mt-4 btn-primary\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"h-4 w-4 mr-2\"}),\"Retry\"]})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:flex md:items-center md:justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\",children:\"Email Transactions\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:data!==null&&data!==void 0&&data.pagination?`Showing ${(data.pagination.currentPage-1)*data.pagination.limit+1}-${Math.min(data.pagination.currentPage*data.pagination.limit,data.pagination.totalCount)} of ${data.pagination.totalCount} transactions`:'Loading transactions...'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 flex space-x-3 md:mt-0 md:ml-4\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>refetch(filters),className:\"btn-outline\",disabled:loading,children:[/*#__PURE__*/_jsx(RefreshCw,{className:`h-4 w-4 mr-2 ${loading?'animate-spin':''}`}),\"Refresh\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"btn-outline\",children:[/*#__PURE__*/_jsx(Download,{className:\"h-4 w-4 mr-2\"}),\"Export\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"card p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSearch,className:\"flex space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(Search,{className:\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search by sender, recipient, subject...\",value:searchInput,onChange:e=>setSearchInput(e.target.value),className:\"input pl-10\"})]})}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn-primary\",children:\"Search\"}),/*#__PURE__*/_jsxs(\"button\",{type:\"button\",onClick:()=>setShowFilters(!showFilters),className:\"btn-outline\",children:[/*#__PURE__*/_jsx(Filter,{className:\"h-4 w-4 mr-2\"}),\"Filters\"]})]}),showFilters&&/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-4 pt-4 border-t border-gray-200 sm:grid-cols-2 lg:grid-cols-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Status\"}),/*#__PURE__*/_jsxs(\"select\",{value:filters.status,onChange:e=>handleFilterChange('status',e.target.value),className:\"select\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Statuses\"}),/*#__PURE__*/_jsx(\"option\",{value:\"delivered\",children:\"Delivered\"}),/*#__PURE__*/_jsx(\"option\",{value:\"relayed\",children:\"Relayed\"}),/*#__PURE__*/_jsx(\"option\",{value:\"queued\",children:\"Queued\"}),/*#__PURE__*/_jsx(\"option\",{value:\"bounced\",children:\"Bounced\"}),/*#__PURE__*/_jsx(\"option\",{value:\"rejected\",children:\"Rejected\"}),/*#__PURE__*/_jsx(\"option\",{value:\"deferred\",children:\"Deferred\"}),/*#__PURE__*/_jsx(\"option\",{value:\"failed\",children:\"Failed\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"From Date\"}),/*#__PURE__*/_jsx(\"input\",{type:\"datetime-local\",value:filters.dateFrom,onChange:e=>handleFilterChange('dateFrom',e.target.value),className:\"input\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"To Date\"}),/*#__PURE__*/_jsx(\"input\",{type:\"datetime-local\",value:filters.dateTo,onChange:e=>handleFilterChange('dateTo',e.target.value),className:\"input\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Per Page\"}),/*#__PURE__*/_jsxs(\"select\",{value:filters.limit,onChange:e=>handleFilterChange('limit',parseInt(e.target.value)),className:\"select\",children:[/*#__PURE__*/_jsx(\"option\",{value:10,children:\"10\"}),/*#__PURE__*/_jsx(\"option\",{value:25,children:\"25\"}),/*#__PURE__*/_jsx(\"option\",{value:50,children:\"50\"}),/*#__PURE__*/_jsx(\"option\",{value:100,children:\"100\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"sm:col-span-2 lg:col-span-4\",children:/*#__PURE__*/_jsx(\"button\",{onClick:clearFilters,className:\"btn-outline\",children:\"Clear Filters\"})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"card overflow-hidden\",children:loading?/*#__PURE__*/_jsxs(\"div\",{className:\"p-12 text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner mx-auto\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-4 text-sm text-gray-500\",children:\"Loading transactions...\"})]}):(data===null||data===void 0?void 0:(_data$transactions=data.transactions)===null||_data$transactions===void 0?void 0:_data$transactions.length)>0?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"table-header\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(SortableHeader,{column:\"timestamp\",children:\"Date/Time\"}),/*#__PURE__*/_jsx(SortableHeader,{column:\"mail_from.address\",children:\"From\"}),/*#__PURE__*/_jsx(\"th\",{className:\"table-header-cell\",children:\"To\"}),/*#__PURE__*/_jsx(SortableHeader,{column:\"message.subject\",children:\"Subject\"}),/*#__PURE__*/_jsx(SortableHeader,{column:\"status\",children:\"Status\"}),/*#__PURE__*/_jsx(SortableHeader,{column:\"message.size\",children:\"Size\"}),/*#__PURE__*/_jsx(SortableHeader,{column:\"processing_time\",children:\"Time\"}),/*#__PURE__*/_jsx(\"th\",{className:\"table-header-cell\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:data.transactions.map(transaction=>{var _transaction$mail_fro,_transaction$connecti,_transaction$rcpt_to,_transaction$rcpt_to$,_transaction$message,_transaction$message2;return/*#__PURE__*/_jsxs(\"tr\",{className:\"table-row\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"table-cell\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:formatDate(transaction.timestamp,'MMM dd, HH:mm')}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:formatRelativeTime(transaction.timestamp)})]})}),/*#__PURE__*/_jsxs(\"td\",{className:\"table-cell\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-900\",children:(_transaction$mail_fro=transaction.mail_from)!==null&&_transaction$mail_fro!==void 0&&_transaction$mail_fro.address?truncateText(formatEmail(transaction.mail_from.address),30):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-400 italic\",children:transaction.status==='disconnected'?'Connection lost':'N/A'})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:((_transaction$connecti=transaction.connection)===null||_transaction$connecti===void 0?void 0:_transaction$connecti.remote_ip)||'Unknown IP'})]}),/*#__PURE__*/_jsx(\"td\",{className:\"table-cell\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-900\",children:((_transaction$rcpt_to=transaction.rcpt_to)===null||_transaction$rcpt_to===void 0?void 0:_transaction$rcpt_to.length)>0?/*#__PURE__*/_jsxs(_Fragment,{children:[truncateText(formatEmail((_transaction$rcpt_to$=transaction.rcpt_to[0])===null||_transaction$rcpt_to$===void 0?void 0:_transaction$rcpt_to$.address),30),transaction.rcpt_to.length>1&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-gray-500\",children:[\" (+\",transaction.rcpt_to.length-1,\")\"]})]}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-400 italic\",children:transaction.status==='disconnected'?'Connection lost':'No recipients'})})}),/*#__PURE__*/_jsx(\"td\",{className:\"table-cell\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-900\",children:(_transaction$message=transaction.message)!==null&&_transaction$message!==void 0&&_transaction$message.subject?truncateText(formatSubject(transaction.message.subject),40):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-400 italic\",children:transaction.status==='disconnected'?'Connection lost':'No Subject'})})}),/*#__PURE__*/_jsx(\"td\",{className:\"table-cell\",children:/*#__PURE__*/_jsx(\"span\",{className:`badge ${getStatusBadge(transaction.status,transaction).className}`,children:getStatusBadge(transaction.status,transaction).label})}),/*#__PURE__*/_jsx(\"td\",{className:\"table-cell text-sm text-gray-500\",children:formatFileSize((_transaction$message2=transaction.message)===null||_transaction$message2===void 0?void 0:_transaction$message2.size)}),/*#__PURE__*/_jsx(\"td\",{className:\"table-cell text-sm text-gray-500\",children:formatProcessingTime(transaction.processing_time)}),/*#__PURE__*/_jsx(\"td\",{className:\"table-cell\",children:/*#__PURE__*/_jsx(Link,{to:`/transactions/${transaction._id}`,className:\"inline-flex items-center justify-center p-2 rounded-md text-primary-600 hover:text-primary-900 hover:bg-primary-50 transition-colors duration-200\",title:\"View transaction details\",\"aria-label\":`View details for transaction ${transaction._id}`,children:/*#__PURE__*/_jsx(Eye,{className:\"h-4 w-4\"})})})]},transaction._id);})})]})}),data.pagination&&data.pagination.totalPages>1&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-white px-4 py-3 border-t border-gray-200 sm:px-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 flex justify-between sm:hidden\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(data.pagination.currentPage-1),disabled:!data.pagination.hasPrevPage,className:\"btn-outline disabled:opacity-50 disabled:cursor-not-allowed\",children:\"Previous\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(data.pagination.currentPage+1),disabled:!data.pagination.hasNextPage,className:\"btn-outline disabled:opacity-50 disabled:cursor-not-allowed\",children:\"Next\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-700\",children:[\"Showing page \",/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:data.pagination.currentPage}),\" of\",' ',/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:data.pagination.totalPages})]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"nav\",{className:\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(data.pagination.currentPage-1),disabled:!data.pagination.hasPrevPage,className:\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",children:/*#__PURE__*/_jsx(ChevronLeft,{className:\"h-5 w-5\"})}),Array.from({length:Math.min(5,data.pagination.totalPages)},(_,i)=>{const pageNum=Math.max(1,data.pagination.currentPage-2)+i;if(pageNum>data.pagination.totalPages)return null;return/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(pageNum),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${pageNum===data.pagination.currentPage?'z-10 bg-primary-50 border-primary-500 text-primary-600':'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}`,children:pageNum},pageNum);}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handlePageChange(data.pagination.currentPage+1),disabled:!data.pagination.hasNextPage,className:\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",children:/*#__PURE__*/_jsx(ChevronRight,{className:\"h-5 w-5\"})})]})})]})]})})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"p-12 text-center\",children:[/*#__PURE__*/_jsx(Mail,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"No transactions found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:filters.search||filters.status||filters.dateFrom||filters.dateTo?'Try adjusting your search criteria or filters.':'No email transactions have been recorded yet.'})]})})]});};export default Transactions;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "Search", "Filter", "ChevronLeft", "ChevronRight", "Mail", "Calendar", "Download", "RefreshCw", "Eye", "ArrowUpDown", "ArrowUp", "ArrowDown", "useTransactions", "formatDate", "formatRelativeTime", "getStatusBadge", "consolidateTransactionStatus", "formatEmail", "formatSubject", "formatFileSize", "formatProcessingTime", "truncateText", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Transactions", "_data$transactions", "filters", "setFilters", "page", "limit", "sortBy", "sortOrder", "search", "status", "dateFrom", "dateTo", "searchInput", "setSearchInput", "showFilters", "setShowFilters", "data", "loading", "error", "refetch", "handleSearch", "e", "preventDefault", "prev", "handleFilterChange", "key", "value", "handleSort", "column", "handlePageChange", "newPage", "clearFilters", "getSortIcon", "className", "Sorta<PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "onClick", "pagination", "currentPage", "Math", "min", "totalCount", "disabled", "onSubmit", "type", "placeholder", "onChange", "target", "parseInt", "transactions", "length", "map", "transaction", "_transaction$mail_fro", "_transaction$connecti", "_transaction$rcpt_to", "_transaction$rcpt_to$", "_transaction$message", "_transaction$message2", "timestamp", "mail_from", "address", "connection", "remote_ip", "rcpt_to", "message", "subject", "label", "size", "processing_time", "to", "_id", "title", "totalPages", "hasPrevPage", "hasNextPage", "Array", "from", "_", "i", "pageNum", "max"], "sources": ["/home/<USER>/haraka/haraka-dashboard/frontend/src/pages/Transactions.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { \n  Search, \n  Filter, \n  ChevronLeft, \n  ChevronRight, \n  Mail, \n  Calendar,\n  Download,\n  RefreshCw,\n  Eye,\n  ArrowUpDown,\n  ArrowUp,\n  ArrowDown\n} from 'lucide-react';\nimport { useTransactions } from '../hooks/useApi';\nimport {\n  formatDate,\n  formatRelativeTime,\n  getStatusBadge,\n  consolidateTransactionStatus,\n  formatEmail,\n  formatSubject,\n  formatFileSize,\n  formatProcessingTime,\n  truncateText\n} from '../utils/formatters';\n\nconst Transactions = () => {\n  const [filters, setFilters] = useState({\n    page: 1,\n    limit: 25,\n    sortBy: 'timestamp',\n    sortOrder: 'desc',\n    search: '',\n    status: '',\n    dateFrom: '',\n    dateTo: ''\n  });\n\n  const [searchInput, setSearchInput] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n\n  const { data, loading, error, refetch } = useTransactions();\n\n  // Fetch data when filters change\n  useEffect(() => {\n    refetch(filters);\n  }, [filters]);\n\n  // Handle search\n  const handleSearch = (e) => {\n    e.preventDefault();\n    setFilters(prev => ({ ...prev, search: searchInput, page: 1 }));\n  };\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));\n  };\n\n  // Handle sorting\n  const handleSort = (column) => {\n    setFilters(prev => ({\n      ...prev,\n      sortBy: column,\n      sortOrder: prev.sortBy === column && prev.sortOrder === 'desc' ? 'asc' : 'desc',\n      page: 1\n    }));\n  };\n\n  // Handle pagination\n  const handlePageChange = (newPage) => {\n    setFilters(prev => ({ ...prev, page: newPage }));\n  };\n\n  // Clear filters\n  const clearFilters = () => {\n    setFilters({\n      page: 1,\n      limit: 25,\n      sortBy: 'timestamp',\n      sortOrder: 'desc',\n      search: '',\n      status: '',\n      dateFrom: '',\n      dateTo: ''\n    });\n    setSearchInput('');\n  };\n\n  // Get sort icon\n  const getSortIcon = (column) => {\n    if (filters.sortBy !== column) {\n      return <ArrowUpDown className=\"h-4 w-4\" />;\n    }\n    return filters.sortOrder === 'desc' ? \n      <ArrowDown className=\"h-4 w-4\" /> : \n      <ArrowUp className=\"h-4 w-4\" />;\n  };\n\n  const SortableHeader = ({ column, children, className = '' }) => (\n    <th\n      className={`table-header-cell ${className}`}\n      onClick={() => handleSort(column)}\n    >\n      <div className=\"flex items-center space-x-1\">\n        <span>{children}</span>\n        {getSortIcon(column)}\n      </div>\n    </th>\n  );\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <Mail className=\"mx-auto h-12 w-12 text-red-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Error loading transactions</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">{error}</p>\n        <button\n          onClick={() => refetch(filters)}\n          className=\"mt-4 btn-primary\"\n        >\n          <RefreshCw className=\"h-4 w-4 mr-2\" />\n          Retry\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"md:flex md:items-center md:justify-between\">\n        <div className=\"flex-1 min-w-0\">\n          <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n            Email Transactions\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            {data?.pagination ? \n              `Showing ${((data.pagination.currentPage - 1) * data.pagination.limit) + 1}-${Math.min(data.pagination.currentPage * data.pagination.limit, data.pagination.totalCount)} of ${data.pagination.totalCount} transactions` :\n              'Loading transactions...'\n            }\n          </p>\n        </div>\n        <div className=\"mt-4 flex space-x-3 md:mt-0 md:ml-4\">\n          <button\n            onClick={() => refetch(filters)}\n            className=\"btn-outline\"\n            disabled={loading}\n          >\n            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />\n            Refresh\n          </button>\n          <button className=\"btn-outline\">\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export\n          </button>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"card p-6\">\n        <div className=\"space-y-4\">\n          {/* Search Bar */}\n          <form onSubmit={handleSearch} className=\"flex space-x-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search by sender, recipient, subject...\"\n                  value={searchInput}\n                  onChange={(e) => setSearchInput(e.target.value)}\n                  className=\"input pl-10\"\n                />\n              </div>\n            </div>\n            <button type=\"submit\" className=\"btn-primary\">\n              Search\n            </button>\n            <button\n              type=\"button\"\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"btn-outline\"\n            >\n              <Filter className=\"h-4 w-4 mr-2\" />\n              Filters\n            </button>\n          </form>\n\n          {/* Advanced Filters */}\n          {showFilters && (\n            <div className=\"grid grid-cols-1 gap-4 pt-4 border-t border-gray-200 sm:grid-cols-2 lg:grid-cols-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Status\n                </label>\n                <select\n                  value={filters.status}\n                  onChange={(e) => handleFilterChange('status', e.target.value)}\n                  className=\"select\"\n                >\n                  <option value=\"\">All Statuses</option>\n                  <option value=\"delivered\">Delivered</option>\n                  <option value=\"relayed\">Relayed</option>\n                  <option value=\"queued\">Queued</option>\n                  <option value=\"bounced\">Bounced</option>\n                  <option value=\"rejected\">Rejected</option>\n                  <option value=\"deferred\">Deferred</option>\n                  <option value=\"failed\">Failed</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  From Date\n                </label>\n                <input\n                  type=\"datetime-local\"\n                  value={filters.dateFrom}\n                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}\n                  className=\"input\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  To Date\n                </label>\n                <input\n                  type=\"datetime-local\"\n                  value={filters.dateTo}\n                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}\n                  className=\"input\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Per Page\n                </label>\n                <select\n                  value={filters.limit}\n                  onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}\n                  className=\"select\"\n                >\n                  <option value={10}>10</option>\n                  <option value={25}>25</option>\n                  <option value={50}>50</option>\n                  <option value={100}>100</option>\n                </select>\n              </div>\n\n              <div className=\"sm:col-span-2 lg:col-span-4\">\n                <button\n                  onClick={clearFilters}\n                  className=\"btn-outline\"\n                >\n                  Clear Filters\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Transactions Table */}\n      <div className=\"card overflow-hidden\">\n        {loading ? (\n          <div className=\"p-12 text-center\">\n            <div className=\"loading-spinner mx-auto\"></div>\n            <p className=\"mt-4 text-sm text-gray-500\">Loading transactions...</p>\n          </div>\n        ) : data?.transactions?.length > 0 ? (\n          <>\n            <div className=\"overflow-x-auto\">\n              <table className=\"table\">\n                <thead className=\"table-header\">\n                  <tr>\n                    <SortableHeader column=\"timestamp\">Date/Time</SortableHeader>\n                    <SortableHeader column=\"mail_from.address\">From</SortableHeader>\n                    <th className=\"table-header-cell\">To</th>\n                    <SortableHeader column=\"message.subject\">Subject</SortableHeader>\n                    <SortableHeader column=\"status\">Status</SortableHeader>\n                    <SortableHeader column=\"message.size\">Size</SortableHeader>\n                    <SortableHeader column=\"processing_time\">Time</SortableHeader>\n                    <th className=\"table-header-cell\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {data.transactions.map((transaction) => (\n                    <tr key={transaction._id} className=\"table-row\">\n                      <td className=\"table-cell\">\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {formatDate(transaction.timestamp, 'MMM dd, HH:mm')}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            {formatRelativeTime(transaction.timestamp)}\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"table-cell\">\n                        <div className=\"text-sm text-gray-900\">\n                          {transaction.mail_from?.address ?\n                            truncateText(formatEmail(transaction.mail_from.address), 30) :\n                            <span className=\"text-gray-400 italic\">\n                              {transaction.status === 'disconnected' ? 'Connection lost' : 'N/A'}\n                            </span>\n                          }\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {transaction.connection?.remote_ip || 'Unknown IP'}\n                        </div>\n                      </td>\n                      <td className=\"table-cell\">\n                        <div className=\"text-sm text-gray-900\">\n                          {transaction.rcpt_to?.length > 0 ? (\n                            <>\n                              {truncateText(formatEmail(transaction.rcpt_to[0]?.address), 30)}\n                              {transaction.rcpt_to.length > 1 && (\n                                <span className=\"text-gray-500\"> (+{transaction.rcpt_to.length - 1})</span>\n                              )}\n                            </>\n                          ) : (\n                            <span className=\"text-gray-400 italic\">\n                              {transaction.status === 'disconnected' ? 'Connection lost' : 'No recipients'}\n                            </span>\n                          )}\n                        </div>\n                      </td>\n                      <td className=\"table-cell\">\n                        <div className=\"text-sm text-gray-900\">\n                          {transaction.message?.subject ?\n                            truncateText(formatSubject(transaction.message.subject), 40) :\n                            <span className=\"text-gray-400 italic\">\n                              {transaction.status === 'disconnected' ? 'Connection lost' : 'No Subject'}\n                            </span>\n                          }\n                        </div>\n                      </td>\n                      <td className=\"table-cell\">\n                        <span className={`badge ${getStatusBadge(transaction.status, transaction).className}`}>\n                          {getStatusBadge(transaction.status, transaction).label}\n                        </span>\n                      </td>\n                      <td className=\"table-cell text-sm text-gray-500\">\n                        {formatFileSize(transaction.message?.size)}\n                      </td>\n                      <td className=\"table-cell text-sm text-gray-500\">\n                        {formatProcessingTime(transaction.processing_time)}\n                      </td>\n                      <td className=\"table-cell\">\n                        <Link\n                          to={`/transactions/${transaction._id}`}\n                          className=\"inline-flex items-center justify-center p-2 rounded-md text-primary-600 hover:text-primary-900 hover:bg-primary-50 transition-colors duration-200\"\n                          title=\"View transaction details\"\n                          aria-label={`View details for transaction ${transaction._id}`}\n                        >\n                          <Eye className=\"h-4 w-4\" />\n                        </Link>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Pagination */}\n            {data.pagination && data.pagination.totalPages > 1 && (\n              <div className=\"bg-white px-4 py-3 border-t border-gray-200 sm:px-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex-1 flex justify-between sm:hidden\">\n                    <button\n                      onClick={() => handlePageChange(data.pagination.currentPage - 1)}\n                      disabled={!data.pagination.hasPrevPage}\n                      className=\"btn-outline disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      Previous\n                    </button>\n                    <button\n                      onClick={() => handlePageChange(data.pagination.currentPage + 1)}\n                      disabled={!data.pagination.hasNextPage}\n                      className=\"btn-outline disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      Next\n                    </button>\n                  </div>\n                  <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                    <div>\n                      <p className=\"text-sm text-gray-700\">\n                        Showing page <span className=\"font-medium\">{data.pagination.currentPage}</span> of{' '}\n                        <span className=\"font-medium\">{data.pagination.totalPages}</span>\n                      </p>\n                    </div>\n                    <div>\n                      <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                        <button\n                          onClick={() => handlePageChange(data.pagination.currentPage - 1)}\n                          disabled={!data.pagination.hasPrevPage}\n                          className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          <ChevronLeft className=\"h-5 w-5\" />\n                        </button>\n\n                        {/* Page numbers */}\n                        {Array.from({ length: Math.min(5, data.pagination.totalPages) }, (_, i) => {\n                          const pageNum = Math.max(1, data.pagination.currentPage - 2) + i;\n                          if (pageNum > data.pagination.totalPages) return null;\n\n                          return (\n                            <button\n                              key={pageNum}\n                              onClick={() => handlePageChange(pageNum)}\n                              className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${\n                                pageNum === data.pagination.currentPage\n                                  ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'\n                                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'\n                              }`}\n                            >\n                              {pageNum}\n                            </button>\n                          );\n                        })}\n\n                        <button\n                          onClick={() => handlePageChange(data.pagination.currentPage + 1)}\n                          disabled={!data.pagination.hasNextPage}\n                          className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          <ChevronRight className=\"h-5 w-5\" />\n                        </button>\n                      </nav>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </>\n        ) : (\n          <div className=\"p-12 text-center\">\n            <Mail className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No transactions found</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {filters.search || filters.status || filters.dateFrom || filters.dateTo\n                ? 'Try adjusting your search criteria or filters.'\n                : 'No email transactions have been recorded yet.'\n              }\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Transactions;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OACEC,MAAM,CACNC,MAAM,CACNC,WAAW,CACXC,YAAY,CACZC,IAAI,CACJC,QAAQ,CACRC,QAAQ,CACRC,SAAS,CACTC,GAAG,CACHC,WAAW,CACXC,OAAO,CACPC,SAAS,KACJ,cAAc,CACrB,OAASC,eAAe,KAAQ,iBAAiB,CACjD,OACEC,UAAU,CACVC,kBAAkB,CAClBC,cAAc,CACdC,4BAA4B,CAC5BC,WAAW,CACXC,aAAa,CACbC,cAAc,CACdC,oBAAoB,CACpBC,YAAY,KACP,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7B,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,KAAAC,kBAAA,CACzB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAC,CACrCmC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,WAAW,CACnBC,SAAS,CAAE,MAAM,CACjBC,MAAM,CAAE,EAAE,CACVC,MAAM,CAAE,EAAE,CACVC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EACV,CAAC,CAAC,CAEF,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC6C,WAAW,CAAEC,cAAc,CAAC,CAAG9C,QAAQ,CAAC,KAAK,CAAC,CAErD,KAAM,CAAE+C,IAAI,CAAEC,OAAO,CAAEC,KAAK,CAAEC,OAAQ,CAAC,CAAGnC,eAAe,CAAC,CAAC,CAE3D;AACAd,SAAS,CAAC,IAAM,CACdiD,OAAO,CAACjB,OAAO,CAAC,CAClB,CAAC,CAAE,CAACA,OAAO,CAAC,CAAC,CAEb;AACA,KAAM,CAAAkB,YAAY,CAAIC,CAAC,EAAK,CAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBnB,UAAU,CAACoB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEf,MAAM,CAAEI,WAAW,CAAER,IAAI,CAAE,CAAE,CAAC,CAAC,CAAC,CACjE,CAAC,CAED;AACA,KAAM,CAAAoB,kBAAkB,CAAGA,CAACC,GAAG,CAAEC,KAAK,GAAK,CACzCvB,UAAU,CAACoB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACE,GAAG,EAAGC,KAAK,CAAEtB,IAAI,CAAE,CAAE,CAAC,CAAC,CAAC,CAC1D,CAAC,CAED;AACA,KAAM,CAAAuB,UAAU,CAAIC,MAAM,EAAK,CAC7BzB,UAAU,CAACoB,IAAI,GAAK,CAClB,GAAGA,IAAI,CACPjB,MAAM,CAAEsB,MAAM,CACdrB,SAAS,CAAEgB,IAAI,CAACjB,MAAM,GAAKsB,MAAM,EAAIL,IAAI,CAAChB,SAAS,GAAK,MAAM,CAAG,KAAK,CAAG,MAAM,CAC/EH,IAAI,CAAE,CACR,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAyB,gBAAgB,CAAIC,OAAO,EAAK,CACpC3B,UAAU,CAACoB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEnB,IAAI,CAAE0B,OAAQ,CAAC,CAAC,CAAC,CAClD,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB5B,UAAU,CAAC,CACTC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,WAAW,CACnBC,SAAS,CAAE,MAAM,CACjBC,MAAM,CAAE,EAAE,CACVC,MAAM,CAAE,EAAE,CACVC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EACV,CAAC,CAAC,CACFE,cAAc,CAAC,EAAE,CAAC,CACpB,CAAC,CAED;AACA,KAAM,CAAAmB,WAAW,CAAIJ,MAAM,EAAK,CAC9B,GAAI1B,OAAO,CAACI,MAAM,GAAKsB,MAAM,CAAE,CAC7B,mBAAOjC,IAAA,CAACd,WAAW,EAACoD,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5C,CACA,MAAO,CAAA/B,OAAO,CAACK,SAAS,GAAK,MAAM,cACjCZ,IAAA,CAACZ,SAAS,EAACkD,SAAS,CAAC,SAAS,CAAE,CAAC,cACjCtC,IAAA,CAACb,OAAO,EAACmD,SAAS,CAAC,SAAS,CAAE,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGC,IAAA,MAAC,CAAEP,MAAM,CAAEQ,QAAQ,CAAEH,SAAS,CAAG,EAAG,CAAC,CAAAE,IAAA,oBAC1DxC,IAAA,OACEsC,SAAS,CAAE,qBAAqBA,SAAS,EAAG,CAC5CI,OAAO,CAAEA,CAAA,GAAMV,UAAU,CAACC,MAAM,CAAE,CAAAQ,QAAA,cAElCvC,KAAA,QAAKoC,SAAS,CAAC,6BAA6B,CAAAG,QAAA,eAC1CzC,IAAA,SAAAyC,QAAA,CAAOA,QAAQ,CAAO,CAAC,CACtBJ,WAAW,CAACJ,MAAM,CAAC,EACjB,CAAC,CACJ,CAAC,EACN,CAED,GAAIV,KAAK,CAAE,CACT,mBACErB,KAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAG,QAAA,eAChCzC,IAAA,CAACnB,IAAI,EAACyD,SAAS,CAAC,gCAAgC,CAAE,CAAC,cACnDtC,IAAA,OAAIsC,SAAS,CAAC,wCAAwC,CAAAG,QAAA,CAAC,4BAA0B,CAAI,CAAC,cACtFzC,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAG,QAAA,CAAElB,KAAK,CAAI,CAAC,cACrDrB,KAAA,WACEwC,OAAO,CAAEA,CAAA,GAAMlB,OAAO,CAACjB,OAAO,CAAE,CAChC+B,SAAS,CAAC,kBAAkB,CAAAG,QAAA,eAE5BzC,IAAA,CAAChB,SAAS,EAACsD,SAAS,CAAC,cAAc,CAAE,CAAC,QAExC,EAAQ,CAAC,EACN,CAAC,CAEV,CAEA,mBACEpC,KAAA,QAAKoC,SAAS,CAAC,WAAW,CAAAG,QAAA,eAExBvC,KAAA,QAAKoC,SAAS,CAAC,4CAA4C,CAAAG,QAAA,eACzDvC,KAAA,QAAKoC,SAAS,CAAC,gBAAgB,CAAAG,QAAA,eAC7BzC,IAAA,OAAIsC,SAAS,CAAC,oEAAoE,CAAAG,QAAA,CAAC,oBAEnF,CAAI,CAAC,cACLzC,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAG,QAAA,CACtCpB,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEsB,UAAU,CACf,WAAY,CAACtB,IAAI,CAACsB,UAAU,CAACC,WAAW,CAAG,CAAC,EAAIvB,IAAI,CAACsB,UAAU,CAACjC,KAAK,CAAI,CAAC,IAAImC,IAAI,CAACC,GAAG,CAACzB,IAAI,CAACsB,UAAU,CAACC,WAAW,CAAGvB,IAAI,CAACsB,UAAU,CAACjC,KAAK,CAAEW,IAAI,CAACsB,UAAU,CAACI,UAAU,CAAC,OAAO1B,IAAI,CAACsB,UAAU,CAACI,UAAU,eAAe,CACvN,yBAAyB,CAE1B,CAAC,EACD,CAAC,cACN7C,KAAA,QAAKoC,SAAS,CAAC,qCAAqC,CAAAG,QAAA,eAClDvC,KAAA,WACEwC,OAAO,CAAEA,CAAA,GAAMlB,OAAO,CAACjB,OAAO,CAAE,CAChC+B,SAAS,CAAC,aAAa,CACvBU,QAAQ,CAAE1B,OAAQ,CAAAmB,QAAA,eAElBzC,IAAA,CAAChB,SAAS,EAACsD,SAAS,CAAE,gBAAgBhB,OAAO,CAAG,cAAc,CAAG,EAAE,EAAG,CAAE,CAAC,UAE3E,EAAQ,CAAC,cACTpB,KAAA,WAAQoC,SAAS,CAAC,aAAa,CAAAG,QAAA,eAC7BzC,IAAA,CAACjB,QAAQ,EAACuD,SAAS,CAAC,cAAc,CAAE,CAAC,SAEvC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGNtC,IAAA,QAAKsC,SAAS,CAAC,UAAU,CAAAG,QAAA,cACvBvC,KAAA,QAAKoC,SAAS,CAAC,WAAW,CAAAG,QAAA,eAExBvC,KAAA,SAAM+C,QAAQ,CAAExB,YAAa,CAACa,SAAS,CAAC,gBAAgB,CAAAG,QAAA,eACtDzC,IAAA,QAAKsC,SAAS,CAAC,QAAQ,CAAAG,QAAA,cACrBvC,KAAA,QAAKoC,SAAS,CAAC,UAAU,CAAAG,QAAA,eACvBzC,IAAA,CAACvB,MAAM,EAAC6D,SAAS,CAAC,0EAA0E,CAAE,CAAC,cAC/FtC,IAAA,UACEkD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,yCAAyC,CACrDpB,KAAK,CAAEd,WAAY,CACnBmC,QAAQ,CAAG1B,CAAC,EAAKR,cAAc,CAACQ,CAAC,CAAC2B,MAAM,CAACtB,KAAK,CAAE,CAChDO,SAAS,CAAC,aAAa,CACxB,CAAC,EACC,CAAC,CACH,CAAC,cACNtC,IAAA,WAAQkD,IAAI,CAAC,QAAQ,CAACZ,SAAS,CAAC,aAAa,CAAAG,QAAA,CAAC,QAE9C,CAAQ,CAAC,cACTvC,KAAA,WACEgD,IAAI,CAAC,QAAQ,CACbR,OAAO,CAAEA,CAAA,GAAMtB,cAAc,CAAC,CAACD,WAAW,CAAE,CAC5CmB,SAAS,CAAC,aAAa,CAAAG,QAAA,eAEvBzC,IAAA,CAACtB,MAAM,EAAC4D,SAAS,CAAC,cAAc,CAAE,CAAC,UAErC,EAAQ,CAAC,EACL,CAAC,CAGNnB,WAAW,eACVjB,KAAA,QAAKoC,SAAS,CAAC,oFAAoF,CAAAG,QAAA,eACjGvC,KAAA,QAAAuC,QAAA,eACEzC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAG,QAAA,CAAC,QAEhE,CAAO,CAAC,cACRvC,KAAA,WACE6B,KAAK,CAAExB,OAAO,CAACO,MAAO,CACtBsC,QAAQ,CAAG1B,CAAC,EAAKG,kBAAkB,CAAC,QAAQ,CAAEH,CAAC,CAAC2B,MAAM,CAACtB,KAAK,CAAE,CAC9DO,SAAS,CAAC,QAAQ,CAAAG,QAAA,eAElBzC,IAAA,WAAQ+B,KAAK,CAAC,EAAE,CAAAU,QAAA,CAAC,cAAY,CAAQ,CAAC,cACtCzC,IAAA,WAAQ+B,KAAK,CAAC,WAAW,CAAAU,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5CzC,IAAA,WAAQ+B,KAAK,CAAC,SAAS,CAAAU,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxCzC,IAAA,WAAQ+B,KAAK,CAAC,QAAQ,CAAAU,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtCzC,IAAA,WAAQ+B,KAAK,CAAC,SAAS,CAAAU,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxCzC,IAAA,WAAQ+B,KAAK,CAAC,UAAU,CAAAU,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1CzC,IAAA,WAAQ+B,KAAK,CAAC,UAAU,CAAAU,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1CzC,IAAA,WAAQ+B,KAAK,CAAC,QAAQ,CAAAU,QAAA,CAAC,QAAM,CAAQ,CAAC,EAChC,CAAC,EACN,CAAC,cAENvC,KAAA,QAAAuC,QAAA,eACEzC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAG,QAAA,CAAC,WAEhE,CAAO,CAAC,cACRzC,IAAA,UACEkD,IAAI,CAAC,gBAAgB,CACrBnB,KAAK,CAAExB,OAAO,CAACQ,QAAS,CACxBqC,QAAQ,CAAG1B,CAAC,EAAKG,kBAAkB,CAAC,UAAU,CAAEH,CAAC,CAAC2B,MAAM,CAACtB,KAAK,CAAE,CAChEO,SAAS,CAAC,OAAO,CAClB,CAAC,EACC,CAAC,cAENpC,KAAA,QAAAuC,QAAA,eACEzC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAG,QAAA,CAAC,SAEhE,CAAO,CAAC,cACRzC,IAAA,UACEkD,IAAI,CAAC,gBAAgB,CACrBnB,KAAK,CAAExB,OAAO,CAACS,MAAO,CACtBoC,QAAQ,CAAG1B,CAAC,EAAKG,kBAAkB,CAAC,QAAQ,CAAEH,CAAC,CAAC2B,MAAM,CAACtB,KAAK,CAAE,CAC9DO,SAAS,CAAC,OAAO,CAClB,CAAC,EACC,CAAC,cAENpC,KAAA,QAAAuC,QAAA,eACEzC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAG,QAAA,CAAC,UAEhE,CAAO,CAAC,cACRvC,KAAA,WACE6B,KAAK,CAAExB,OAAO,CAACG,KAAM,CACrB0C,QAAQ,CAAG1B,CAAC,EAAKG,kBAAkB,CAAC,OAAO,CAAEyB,QAAQ,CAAC5B,CAAC,CAAC2B,MAAM,CAACtB,KAAK,CAAC,CAAE,CACvEO,SAAS,CAAC,QAAQ,CAAAG,QAAA,eAElBzC,IAAA,WAAQ+B,KAAK,CAAE,EAAG,CAAAU,QAAA,CAAC,IAAE,CAAQ,CAAC,cAC9BzC,IAAA,WAAQ+B,KAAK,CAAE,EAAG,CAAAU,QAAA,CAAC,IAAE,CAAQ,CAAC,cAC9BzC,IAAA,WAAQ+B,KAAK,CAAE,EAAG,CAAAU,QAAA,CAAC,IAAE,CAAQ,CAAC,cAC9BzC,IAAA,WAAQ+B,KAAK,CAAE,GAAI,CAAAU,QAAA,CAAC,KAAG,CAAQ,CAAC,EAC1B,CAAC,EACN,CAAC,cAENzC,IAAA,QAAKsC,SAAS,CAAC,6BAA6B,CAAAG,QAAA,cAC1CzC,IAAA,WACE0C,OAAO,CAAEN,YAAa,CACtBE,SAAS,CAAC,aAAa,CAAAG,QAAA,CACxB,eAED,CAAQ,CAAC,CACN,CAAC,EACH,CACN,EACE,CAAC,CACH,CAAC,cAGNzC,IAAA,QAAKsC,SAAS,CAAC,sBAAsB,CAAAG,QAAA,CAClCnB,OAAO,cACNpB,KAAA,QAAKoC,SAAS,CAAC,kBAAkB,CAAAG,QAAA,eAC/BzC,IAAA,QAAKsC,SAAS,CAAC,yBAAyB,CAAM,CAAC,cAC/CtC,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAG,QAAA,CAAC,yBAAuB,CAAG,CAAC,EAClE,CAAC,CACJ,CAAApB,IAAI,SAAJA,IAAI,kBAAAf,kBAAA,CAAJe,IAAI,CAAEkC,YAAY,UAAAjD,kBAAA,iBAAlBA,kBAAA,CAAoBkD,MAAM,EAAG,CAAC,cAChCtD,KAAA,CAAAE,SAAA,EAAAqC,QAAA,eACEzC,IAAA,QAAKsC,SAAS,CAAC,iBAAiB,CAAAG,QAAA,cAC9BvC,KAAA,UAAOoC,SAAS,CAAC,OAAO,CAAAG,QAAA,eACtBzC,IAAA,UAAOsC,SAAS,CAAC,cAAc,CAAAG,QAAA,cAC7BvC,KAAA,OAAAuC,QAAA,eACEzC,IAAA,CAACuC,cAAc,EAACN,MAAM,CAAC,WAAW,CAAAQ,QAAA,CAAC,WAAS,CAAgB,CAAC,cAC7DzC,IAAA,CAACuC,cAAc,EAACN,MAAM,CAAC,mBAAmB,CAAAQ,QAAA,CAAC,MAAI,CAAgB,CAAC,cAChEzC,IAAA,OAAIsC,SAAS,CAAC,mBAAmB,CAAAG,QAAA,CAAC,IAAE,CAAI,CAAC,cACzCzC,IAAA,CAACuC,cAAc,EAACN,MAAM,CAAC,iBAAiB,CAAAQ,QAAA,CAAC,SAAO,CAAgB,CAAC,cACjEzC,IAAA,CAACuC,cAAc,EAACN,MAAM,CAAC,QAAQ,CAAAQ,QAAA,CAAC,QAAM,CAAgB,CAAC,cACvDzC,IAAA,CAACuC,cAAc,EAACN,MAAM,CAAC,cAAc,CAAAQ,QAAA,CAAC,MAAI,CAAgB,CAAC,cAC3DzC,IAAA,CAACuC,cAAc,EAACN,MAAM,CAAC,iBAAiB,CAAAQ,QAAA,CAAC,MAAI,CAAgB,CAAC,cAC9DzC,IAAA,OAAIsC,SAAS,CAAC,mBAAmB,CAAAG,QAAA,CAAC,SAAO,CAAI,CAAC,EAC5C,CAAC,CACA,CAAC,cACRzC,IAAA,UAAOsC,SAAS,CAAC,mCAAmC,CAAAG,QAAA,CACjDpB,IAAI,CAACkC,YAAY,CAACE,GAAG,CAAEC,WAAW,OAAAC,qBAAA,CAAAC,qBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,oBAAA,CAAAC,qBAAA,oBACjC9D,KAAA,OAA0BoC,SAAS,CAAC,WAAW,CAAAG,QAAA,eAC7CzC,IAAA,OAAIsC,SAAS,CAAC,YAAY,CAAAG,QAAA,cACxBvC,KAAA,QAAAuC,QAAA,eACEzC,IAAA,QAAKsC,SAAS,CAAC,mCAAmC,CAAAG,QAAA,CAC/CnD,UAAU,CAACoE,WAAW,CAACO,SAAS,CAAE,eAAe,CAAC,CAChD,CAAC,cACNjE,IAAA,QAAKsC,SAAS,CAAC,uBAAuB,CAAAG,QAAA,CACnClD,kBAAkB,CAACmE,WAAW,CAACO,SAAS,CAAC,CACvC,CAAC,EACH,CAAC,CACJ,CAAC,cACL/D,KAAA,OAAIoC,SAAS,CAAC,YAAY,CAAAG,QAAA,eACxBzC,IAAA,QAAKsC,SAAS,CAAC,uBAAuB,CAAAG,QAAA,CACnC,CAAAkB,qBAAA,CAAAD,WAAW,CAACQ,SAAS,UAAAP,qBAAA,WAArBA,qBAAA,CAAuBQ,OAAO,CAC7BrE,YAAY,CAACJ,WAAW,CAACgE,WAAW,CAACQ,SAAS,CAACC,OAAO,CAAC,CAAE,EAAE,CAAC,cAC5DnE,IAAA,SAAMsC,SAAS,CAAC,sBAAsB,CAAAG,QAAA,CACnCiB,WAAW,CAAC5C,MAAM,GAAK,cAAc,CAAG,iBAAiB,CAAG,KAAK,CAC9D,CAAC,CAEN,CAAC,cACNd,IAAA,QAAKsC,SAAS,CAAC,uBAAuB,CAAAG,QAAA,CACnC,EAAAmB,qBAAA,CAAAF,WAAW,CAACU,UAAU,UAAAR,qBAAA,iBAAtBA,qBAAA,CAAwBS,SAAS,GAAI,YAAY,CAC/C,CAAC,EACJ,CAAC,cACLrE,IAAA,OAAIsC,SAAS,CAAC,YAAY,CAAAG,QAAA,cACxBzC,IAAA,QAAKsC,SAAS,CAAC,uBAAuB,CAAAG,QAAA,CACnC,EAAAoB,oBAAA,CAAAH,WAAW,CAACY,OAAO,UAAAT,oBAAA,iBAAnBA,oBAAA,CAAqBL,MAAM,EAAG,CAAC,cAC9BtD,KAAA,CAAAE,SAAA,EAAAqC,QAAA,EACG3C,YAAY,CAACJ,WAAW,EAAAoE,qBAAA,CAACJ,WAAW,CAACY,OAAO,CAAC,CAAC,CAAC,UAAAR,qBAAA,iBAAtBA,qBAAA,CAAwBK,OAAO,CAAC,CAAE,EAAE,CAAC,CAC9DT,WAAW,CAACY,OAAO,CAACd,MAAM,CAAG,CAAC,eAC7BtD,KAAA,SAAMoC,SAAS,CAAC,eAAe,CAAAG,QAAA,EAAC,KAAG,CAACiB,WAAW,CAACY,OAAO,CAACd,MAAM,CAAG,CAAC,CAAC,GAAC,EAAM,CAC3E,EACD,CAAC,cAEHxD,IAAA,SAAMsC,SAAS,CAAC,sBAAsB,CAAAG,QAAA,CACnCiB,WAAW,CAAC5C,MAAM,GAAK,cAAc,CAAG,iBAAiB,CAAG,eAAe,CACxE,CACP,CACE,CAAC,CACJ,CAAC,cACLd,IAAA,OAAIsC,SAAS,CAAC,YAAY,CAAAG,QAAA,cACxBzC,IAAA,QAAKsC,SAAS,CAAC,uBAAuB,CAAAG,QAAA,CACnC,CAAAsB,oBAAA,CAAAL,WAAW,CAACa,OAAO,UAAAR,oBAAA,WAAnBA,oBAAA,CAAqBS,OAAO,CAC3B1E,YAAY,CAACH,aAAa,CAAC+D,WAAW,CAACa,OAAO,CAACC,OAAO,CAAC,CAAE,EAAE,CAAC,cAC5DxE,IAAA,SAAMsC,SAAS,CAAC,sBAAsB,CAAAG,QAAA,CACnCiB,WAAW,CAAC5C,MAAM,GAAK,cAAc,CAAG,iBAAiB,CAAG,YAAY,CACrE,CAAC,CAEN,CAAC,CACJ,CAAC,cACLd,IAAA,OAAIsC,SAAS,CAAC,YAAY,CAAAG,QAAA,cACxBzC,IAAA,SAAMsC,SAAS,CAAE,SAAS9C,cAAc,CAACkE,WAAW,CAAC5C,MAAM,CAAE4C,WAAW,CAAC,CAACpB,SAAS,EAAG,CAAAG,QAAA,CACnFjD,cAAc,CAACkE,WAAW,CAAC5C,MAAM,CAAE4C,WAAW,CAAC,CAACe,KAAK,CAClD,CAAC,CACL,CAAC,cACLzE,IAAA,OAAIsC,SAAS,CAAC,kCAAkC,CAAAG,QAAA,CAC7C7C,cAAc,EAAAoE,qBAAA,CAACN,WAAW,CAACa,OAAO,UAAAP,qBAAA,iBAAnBA,qBAAA,CAAqBU,IAAI,CAAC,CACxC,CAAC,cACL1E,IAAA,OAAIsC,SAAS,CAAC,kCAAkC,CAAAG,QAAA,CAC7C5C,oBAAoB,CAAC6D,WAAW,CAACiB,eAAe,CAAC,CAChD,CAAC,cACL3E,IAAA,OAAIsC,SAAS,CAAC,YAAY,CAAAG,QAAA,cACxBzC,IAAA,CAACxB,IAAI,EACHoG,EAAE,CAAE,iBAAiBlB,WAAW,CAACmB,GAAG,EAAG,CACvCvC,SAAS,CAAC,mJAAmJ,CAC7JwC,KAAK,CAAC,0BAA0B,CAChC,aAAY,gCAAgCpB,WAAW,CAACmB,GAAG,EAAG,CAAApC,QAAA,cAE9DzC,IAAA,CAACf,GAAG,EAACqD,SAAS,CAAC,SAAS,CAAE,CAAC,CACvB,CAAC,CACL,CAAC,GAtEEoB,WAAW,CAACmB,GAuEjB,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAGLxD,IAAI,CAACsB,UAAU,EAAItB,IAAI,CAACsB,UAAU,CAACoC,UAAU,CAAG,CAAC,eAChD/E,IAAA,QAAKsC,SAAS,CAAC,qDAAqD,CAAAG,QAAA,cAClEvC,KAAA,QAAKoC,SAAS,CAAC,mCAAmC,CAAAG,QAAA,eAChDvC,KAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAG,QAAA,eACpDzC,IAAA,WACE0C,OAAO,CAAEA,CAAA,GAAMR,gBAAgB,CAACb,IAAI,CAACsB,UAAU,CAACC,WAAW,CAAG,CAAC,CAAE,CACjEI,QAAQ,CAAE,CAAC3B,IAAI,CAACsB,UAAU,CAACqC,WAAY,CACvC1C,SAAS,CAAC,6DAA6D,CAAAG,QAAA,CACxE,UAED,CAAQ,CAAC,cACTzC,IAAA,WACE0C,OAAO,CAAEA,CAAA,GAAMR,gBAAgB,CAACb,IAAI,CAACsB,UAAU,CAACC,WAAW,CAAG,CAAC,CAAE,CACjEI,QAAQ,CAAE,CAAC3B,IAAI,CAACsB,UAAU,CAACsC,WAAY,CACvC3C,SAAS,CAAC,6DAA6D,CAAAG,QAAA,CACxE,MAED,CAAQ,CAAC,EACN,CAAC,cACNvC,KAAA,QAAKoC,SAAS,CAAC,6DAA6D,CAAAG,QAAA,eAC1EzC,IAAA,QAAAyC,QAAA,cACEvC,KAAA,MAAGoC,SAAS,CAAC,uBAAuB,CAAAG,QAAA,EAAC,eACtB,cAAAzC,IAAA,SAAMsC,SAAS,CAAC,aAAa,CAAAG,QAAA,CAAEpB,IAAI,CAACsB,UAAU,CAACC,WAAW,CAAO,CAAC,MAAG,CAAC,GAAG,cACtF5C,IAAA,SAAMsC,SAAS,CAAC,aAAa,CAAAG,QAAA,CAAEpB,IAAI,CAACsB,UAAU,CAACoC,UAAU,CAAO,CAAC,EAChE,CAAC,CACD,CAAC,cACN/E,IAAA,QAAAyC,QAAA,cACEvC,KAAA,QAAKoC,SAAS,CAAC,2DAA2D,CAAAG,QAAA,eACxEzC,IAAA,WACE0C,OAAO,CAAEA,CAAA,GAAMR,gBAAgB,CAACb,IAAI,CAACsB,UAAU,CAACC,WAAW,CAAG,CAAC,CAAE,CACjEI,QAAQ,CAAE,CAAC3B,IAAI,CAACsB,UAAU,CAACqC,WAAY,CACvC1C,SAAS,CAAC,6LAA6L,CAAAG,QAAA,cAEvMzC,IAAA,CAACrB,WAAW,EAAC2D,SAAS,CAAC,SAAS,CAAE,CAAC,CAC7B,CAAC,CAGR4C,KAAK,CAACC,IAAI,CAAC,CAAE3B,MAAM,CAAEX,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEzB,IAAI,CAACsB,UAAU,CAACoC,UAAU,CAAE,CAAC,CAAE,CAACK,CAAC,CAAEC,CAAC,GAAK,CACzE,KAAM,CAAAC,OAAO,CAAGzC,IAAI,CAAC0C,GAAG,CAAC,CAAC,CAAElE,IAAI,CAACsB,UAAU,CAACC,WAAW,CAAG,CAAC,CAAC,CAAGyC,CAAC,CAChE,GAAIC,OAAO,CAAGjE,IAAI,CAACsB,UAAU,CAACoC,UAAU,CAAE,MAAO,KAAI,CAErD,mBACE/E,IAAA,WAEE0C,OAAO,CAAEA,CAAA,GAAMR,gBAAgB,CAACoD,OAAO,CAAE,CACzChD,SAAS,CAAE,0EACTgD,OAAO,GAAKjE,IAAI,CAACsB,UAAU,CAACC,WAAW,CACnC,wDAAwD,CACxD,yDAAyD,EAC5D,CAAAH,QAAA,CAEF6C,OAAO,EARHA,OASC,CAAC,CAEb,CAAC,CAAC,cAEFtF,IAAA,WACE0C,OAAO,CAAEA,CAAA,GAAMR,gBAAgB,CAACb,IAAI,CAACsB,UAAU,CAACC,WAAW,CAAG,CAAC,CAAE,CACjEI,QAAQ,CAAE,CAAC3B,IAAI,CAACsB,UAAU,CAACsC,WAAY,CACvC3C,SAAS,CAAC,6LAA6L,CAAAG,QAAA,cAEvMzC,IAAA,CAACpB,YAAY,EAAC0D,SAAS,CAAC,SAAS,CAAE,CAAC,CAC9B,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,EACD,CAAC,cAEHpC,KAAA,QAAKoC,SAAS,CAAC,kBAAkB,CAAAG,QAAA,eAC/BzC,IAAA,CAACnB,IAAI,EAACyD,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACpDtC,IAAA,OAAIsC,SAAS,CAAC,wCAAwC,CAAAG,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACjFzC,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAG,QAAA,CACtClC,OAAO,CAACM,MAAM,EAAIN,OAAO,CAACO,MAAM,EAAIP,OAAO,CAACQ,QAAQ,EAAIR,OAAO,CAACS,MAAM,CACnE,gDAAgD,CAChD,+CAA+C,CAElD,CAAC,EACD,CACN,CACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAX,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}